import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';
import '../styles/BillSplitPage.css';

const BillSplitPage = () => {
  const { orderId } = useParams();
  const [billData, setBillData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedParticipant, setSelectedParticipant] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('card');

  useEffect(() => {
    fetchBillSplitData();
  }, [orderId]);

  const fetchBillSplitData = async () => {
    try {
      const response = await axios.get(`/api/orders/${orderId}/split`);
      setBillData(response.data);
    } catch (error) {
      toast.error('Failed to load bill split data');
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async (participantId) => {
    try {
      await axios.put(`/api/orders/${orderId}/split/participant/${participantId}`, {
        paymentStatus: 'paid',
        paymentMethod
      });
      toast.success('Payment marked as completed!');
      fetchBillSplitData(); // Refresh data
    } catch (error) {
      toast.error('Failed to update payment status');
    }
  };

  const getPaymentStatusColor = (status) => {
    const colors = {
      pending: '#f39c12',
      paid: '#27ae60',
      failed: '#e74c3c'
    };
    return colors[status] || '#95a5a6';
  };

  if (loading) {
    return (
      <div className="bill-split-page">
        <div className="container">
          <div className="loading">Loading bill split details...</div>
        </div>
      </div>
    );
  }

  if (!billData) {
    return (
      <div className="bill-split-page">
        <div className="container">
          <div className="error-message">
            <h2>Bill Split Not Found</h2>
            <p>The bill split for this order could not be found or is not enabled.</p>
          </div>
        </div>
      </div>
    );
  }

  const totalPaid = billData.billSplit.participants.filter(p => p.paymentStatus === 'paid').length;
  const totalParticipants = billData.billSplit.participants.length;
  const allPaid = totalPaid === totalParticipants;

  return (
    <div className="bill-split-page">
      <div className="container">
        <div className="bill-split-header">
          <div className="restaurant-info">
            <h1>{billData.restaurant.name}</h1>
            <p>{billData.restaurant.address}</p>
            <p>{billData.restaurant.phone}</p>
          </div>
          
          <div className="order-info">
            <h2>Order #{billData.orderNumber}</h2>
            <div className="payment-progress">
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${(totalPaid / totalParticipants) * 100}%` }}
                ></div>
              </div>
              <p>{totalPaid} of {totalParticipants} people have paid</p>
            </div>
          </div>
        </div>

        <div className="bill-summary">
          <div className="order-items">
            <h3>Order Items</h3>
            <div className="items-list">
              {billData.items.map((item, index) => (
                <div key={index} className="item-row">
                  <div className="item-info">
                    <img 
                      src={`http://localhost:5000/uploads/${item.dish.image}`}
                      alt={item.dish.name}
                      className="item-image"
                    />
                    <div className="item-details">
                      <h4>{item.dish.name}</h4>
                      <p>Quantity: {item.quantity}</p>
                      <p>Price: ${item.price} each</p>
                    </div>
                  </div>
                  <div className="item-total">
                    ${(item.price * item.quantity).toFixed(2)}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="bill-totals">
              <div className="total-row">
                <span>Subtotal:</span>
                <span>${billData.totalAmount.toFixed(2)}</span>
              </div>
              {billData.billSplit.splitDetails.tax > 0 && (
                <div className="total-row">
                  <span>Tax:</span>
                  <span>${billData.billSplit.splitDetails.tax.toFixed(2)}</span>
                </div>
              )}
              {billData.billSplit.splitDetails.tip > 0 && (
                <div className="total-row">
                  <span>Tip:</span>
                  <span>${billData.billSplit.splitDetails.tip.toFixed(2)}</span>
                </div>
              )}
              {billData.billSplit.splitDetails.serviceFee > 0 && (
                <div className="total-row">
                  <span>Service Fee:</span>
                  <span>${billData.billSplit.splitDetails.serviceFee.toFixed(2)}</span>
                </div>
              )}
              <div className="total-row grand-total">
                <span>Total:</span>
                <span>${(billData.totalAmount + 
                  (billData.billSplit.splitDetails.tax || 0) + 
                  (billData.billSplit.splitDetails.tip || 0) + 
                  (billData.billSplit.splitDetails.serviceFee || 0)
                ).toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div className="split-info">
            <h3>Split Details</h3>
            <div className="split-summary-info">
              <p><strong>Split Type:</strong> {billData.billSplit.splitType.replace('-', ' ').toUpperCase()}</p>
              <p><strong>Total People:</strong> {billData.billSplit.totalPeople}</p>
              {billData.billSplit.splitDetails.perPersonAmount && (
                <p><strong>Per Person:</strong> ${billData.billSplit.splitDetails.perPersonAmount.toFixed(2)}</p>
              )}
            </div>
          </div>
        </div>

        <div className="participants-section">
          <h3>Who Owes What</h3>
          
          {allPaid && (
            <div className="all-paid-banner">
              <h2>🎉 All payments completed!</h2>
              <p>Everyone has paid their share. Thank you!</p>
            </div>
          )}

          <div className="participants-grid">
            {billData.billSplit.participants.map((participant, index) => (
              <div 
                key={index} 
                className={`participant-card ${participant.paymentStatus}`}
              >
                <div className="participant-header">
                  <h4>{participant.name}</h4>
                  <div 
                    className="status-badge"
                    style={{ backgroundColor: getPaymentStatusColor(participant.paymentStatus) }}
                  >
                    {participant.paymentStatus.toUpperCase()}
                  </div>
                </div>

                <div className="participant-amount">
                  <span className="amount">${participant.amount.toFixed(2)}</span>
                </div>

                {participant.items && participant.items.length > 0 && (
                  <div className="participant-items">
                    <h5>Their Items:</h5>
                    {participant.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="participant-item">
                        <span>{item.dish.name} x{item.quantity}</span>
                        <span>${(item.price * item.quantity).toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                )}

                {participant.paymentStatus === 'pending' && (
                  <div className="payment-section">
                    <div className="payment-methods">
                      <label>Payment Method:</label>
                      <select 
                        value={paymentMethod} 
                        onChange={(e) => setPaymentMethod(e.target.value)}
                      >
                        <option value="card">Credit/Debit Card</option>
                        <option value="cash">Cash</option>
                        <option value="venmo">Venmo</option>
                        <option value="paypal">PayPal</option>
                        <option value="online">Online Transfer</option>
                      </select>
                    </div>
                    
                    <button 
                      className="pay-btn"
                      onClick={() => handlePayment(participant._id)}
                    >
                      Mark as Paid
                    </button>
                  </div>
                )}

                {participant.paymentStatus === 'paid' && (
                  <div className="paid-info">
                    <p>✅ Paid via {participant.paymentMethod}</p>
                    <p className="paid-time">
                      {new Date(participant.paidAt).toLocaleString()}
                    </p>
                  </div>
                )}

                {participant.email && (
                  <div className="contact-info">
                    <p>📧 {participant.email}</p>
                  </div>
                )}
                {participant.phone && (
                  <div className="contact-info">
                    <p>📞 {participant.phone}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="bill-split-footer">
          <div className="share-section">
            <h4>Share this bill split:</h4>
            <div className="share-link">
              <input 
                type="text" 
                value={window.location.href} 
                readOnly 
                className="share-input"
              />
              <button 
                className="copy-btn"
                onClick={() => {
                  navigator.clipboard.writeText(window.location.href);
                  toast.success('Link copied to clipboard!');
                }}
              >
                Copy Link
              </button>
            </div>
          </div>

          <div className="help-section">
            <h4>Need help?</h4>
            <p>Contact the restaurant at {billData.restaurant.phone}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillSplitPage;
