import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import { toast } from 'react-toastify';
import '../styles/Cart.css';

const Cart = ({ isOpen, onClose }) => {
  const { items, totalAmount, updateQuantity, removeFromCart, clearCart } = useCart();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [orderType, setOrderType] = useState('delivery');
  const [deliveryAddress, setDeliveryAddress] = useState({
    street: '',
    city: '',
    state: '',
    zipCode: '',
    phone: ''
  });

  const handleQuantityChange = (dishId, newQuantity) => {
    if (newQuantity < 1) {
      removeFrom<PERSON>art(dishId);
    } else {
      updateQuantity(dishId, newQuantity);
    }
  };

  const handleAddressChange = (e) => {
    setDeliveryAddress({
      ...deliveryAddress,
      [e.target.name]: e.target.value
    });
  };

  const handleCheckout = async () => {
    if (!isAuthenticated) {
      toast.info('Please login to place an order');
      navigate('/login');
      onClose();
      return;
    }

    if (items.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    if (orderType === 'delivery' && (!deliveryAddress.street || !deliveryAddress.city || !deliveryAddress.phone)) {
      toast.error('Please fill in all delivery address fields');
      return;
    }

    setIsLoading(true);

    try {
      const orderData = {
        items: items.map(item => ({
          dish: item.dish._id,
          quantity: item.quantity,
          specialInstructions: item.specialInstructions || ''
        })),
        orderType,
        paymentMethod: 'card', // Default to card payment
        deliveryAddress: orderType === 'delivery' ? deliveryAddress : undefined
      };

      const response = await axios.post('/api/orders', orderData);
      
      toast.success('Order placed successfully!');
      clearCart();
      onClose();
      navigate('/orders');
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error(error.response?.data?.message || 'Failed to place order');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="cart-overlay" onClick={onClose}>
      <div className="cart-sidebar" onClick={(e) => e.stopPropagation()}>
        <div className="cart-header">
          <h2>Shopping Cart</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="cart-content">
          {items.length === 0 ? (
            <div className="empty-cart">
              <p>Your cart is empty</p>
              <button className="continue-shopping-btn" onClick={onClose}>
                Continue Shopping
              </button>
            </div>
          ) : (
            <>
              <div className="cart-items">
                {items.map((item) => (
                  <div key={item.dish._id} className="cart-item">
                    <img 
                      src={`http://localhost:5000/uploads/${item.dish.image}`} 
                      alt={item.dish.name}
                      className="cart-item-image"
                    />
                    <div className="cart-item-details">
                      <h4>{item.dish.name}</h4>
                      <p className="cart-item-price">${item.dish.price}</p>
                      {item.specialInstructions && (
                        <p className="special-instructions">
                          Note: {item.specialInstructions}
                        </p>
                      )}
                    </div>
                    <div className="cart-item-controls">
                      <div className="quantity-controls">
                        <button 
                          onClick={() => handleQuantityChange(item.dish._id, item.quantity - 1)}
                          className="quantity-btn"
                        >
                          -
                        </button>
                        <span className="quantity">{item.quantity}</span>
                        <button 
                          onClick={() => handleQuantityChange(item.dish._id, item.quantity + 1)}
                          className="quantity-btn"
                        >
                          +
                        </button>
                      </div>
                      <button 
                        onClick={() => removeFromCart(item.dish._id)}
                        className="remove-btn"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="order-options">
                <h3>Order Type</h3>
                <div className="order-type-options">
                  <label>
                    <input
                      type="radio"
                      value="delivery"
                      checked={orderType === 'delivery'}
                      onChange={(e) => setOrderType(e.target.value)}
                    />
                    Delivery
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="pickup"
                      checked={orderType === 'pickup'}
                      onChange={(e) => setOrderType(e.target.value)}
                    />
                    Pickup
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="dine-in"
                      checked={orderType === 'dine-in'}
                      onChange={(e) => setOrderType(e.target.value)}
                    />
                    Dine In
                  </label>
                </div>

                {orderType === 'delivery' && (
                  <div className="delivery-address">
                    <h4>Delivery Address</h4>
                    <input
                      type="text"
                      name="street"
                      placeholder="Street Address"
                      value={deliveryAddress.street}
                      onChange={handleAddressChange}
                      required
                    />
                    <input
                      type="text"
                      name="city"
                      placeholder="City"
                      value={deliveryAddress.city}
                      onChange={handleAddressChange}
                      required
                    />
                    <input
                      type="text"
                      name="state"
                      placeholder="State"
                      value={deliveryAddress.state}
                      onChange={handleAddressChange}
                    />
                    <input
                      type="text"
                      name="zipCode"
                      placeholder="ZIP Code"
                      value={deliveryAddress.zipCode}
                      onChange={handleAddressChange}
                    />
                    <input
                      type="tel"
                      name="phone"
                      placeholder="Phone Number"
                      value={deliveryAddress.phone}
                      onChange={handleAddressChange}
                      required
                    />
                  </div>
                )}
              </div>

              <div className="cart-footer">
                <div className="cart-total">
                  <h3>Total: ${totalAmount.toFixed(2)}</h3>
                </div>
                <div className="cart-actions">
                  <button 
                    className="clear-cart-btn" 
                    onClick={clearCart}
                    disabled={isLoading}
                  >
                    Clear Cart
                  </button>
                  <button 
                    className="checkout-btn" 
                    onClick={handleCheckout}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Processing...' : 'Checkout'}
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Cart;
