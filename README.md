# Restaurant Website - MERN Stack

A full-featured restaurant website built with the MERN stack (MongoDB, Express.js, React, Node.js) featuring online ordering, table reservations, and admin management.

## Features

### Customer Features
- 🏠 **Homepage** with featured dishes and restaurant information
- 🍽️ **Menu browsing** with categories, search, and filters
- 🛒 **Shopping cart** with order customization
- 📦 **Online ordering** (delivery, pickup, dine-in)
- 📅 **Table reservations** with availability checking
- 👤 **User authentication** and profile management
- 📱 **Responsive design** for mobile and desktop
- 💬 **Contact form** for customer inquiries

### Admin Features
- 📊 **Admin dashboard** with analytics
- 🍽️ **Menu management** (CRUD operations)
- 📦 **Order management** with real-time status updates
- 📅 **Reservation management** with table assignments
- 💬 **Contact message management**
- 🖼️ **Image upload** for dishes
- 🔐 **Secure admin authentication**

### Technical Features
- 🔒 **JWT authentication** with role-based access
- 📡 **Real-time updates** with Socket.io
- 🖼️ **Image upload** with Multer
- ✅ **Form validation** on frontend and backend
- 🛡️ **Security** with Helmet and CORS
- 📱 **Mobile-first responsive design**
- 🎨 **Modern UI/UX** with smooth animations

## Tech Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB
- **JWT** - Authentication
- **Bcrypt** - Password hashing
- **Multer** - File upload handling
- **Socket.io** - Real-time communication
- **Helmet** - Security middleware
- **CORS** - Cross-origin resource sharing

### Frontend
- **React** - UI library
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **Socket.io Client** - Real-time communication
- **React Toastify** - Notifications
- **CSS3** - Styling with modern features

## Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- Git

### 1. Clone the Repository
```bash
git clone <repository-url>
cd restaurant-app
```

### 2. Backend Setup
```bash
cd backend
npm install
```

Create a `.env` file in the backend directory:
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/restaurant
JWT_SECRET=your_jwt_secret_key_here_change_in_production
JWT_EXPIRE=30d
FRONTEND_URL=http://localhost:3000

# Email configuration (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

### 3. Frontend Setup
```bash
cd ../frontend
npm install
```

Create a `.env` file in the frontend directory:
```env
REACT_APP_API_URL=http://localhost:5000
```

### 4. Database Setup

Start MongoDB service, then seed the database with sample data:
```bash
cd ../backend
node scripts/seed.js
```

### 5. Start the Application

**Backend (Terminal 1):**
```bash
cd backend
npm run dev
```

**Frontend (Terminal 2):**
```bash
cd frontend
npm start
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## Sample Login Credentials

After running the seed script, you can use these credentials:

**Admin Account:**
- Email: <EMAIL>
- Password: admin123

**Customer Account:**
- Email: <EMAIL>
- Password: customer123

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Menu
- `GET /api/menu` - Get all dishes (with filters)
- `GET /api/menu/:id` - Get single dish
- `POST /api/menu` - Create dish (Admin)
- `PUT /api/menu/:id` - Update dish (Admin)
- `DELETE /api/menu/:id` - Delete dish (Admin)
- `GET /api/menu/categories` - Get categories

### Orders
- `POST /api/orders` - Create order
- `GET /api/orders` - Get user orders
- `GET /api/orders/:id` - Get single order
- `GET /api/orders/admin/all` - Get all orders (Admin)
- `PUT /api/orders/:id/status` - Update order status (Admin)
- `PUT /api/orders/:id/cancel` - Cancel order

### Reservations
- `POST /api/reservations` - Create reservation
- `GET /api/reservations` - Get user reservations
- `GET /api/reservations/:id` - Get single reservation
- `GET /api/reservations/admin/all` - Get all reservations (Admin)
- `PUT /api/reservations/:id/status` - Update reservation status (Admin)
- `PUT /api/reservations/:id/cancel` - Cancel reservation
- `GET /api/reservations/availability` - Check availability

### Contact
- `POST /api/contact` - Submit contact form
- `GET /api/contact/admin/all` - Get all messages (Admin)
- `GET /api/contact/:id` - Get single message (Admin)
- `PUT /api/contact/:id/status` - Update message status (Admin)
- `POST /api/contact/:id/reply` - Reply to message (Admin)
- `DELETE /api/contact/:id` - Delete message (Admin)

## Project Structure

```
restaurant-app/
├── backend/
│   ├── config/
│   │   └── db.js
│   ├── controllers/
│   │   ├── authController.js
│   │   ├── menuController.js
│   │   ├── orderController.js
│   │   ├── reservationController.js
│   │   └── contactController.js
│   ├── middleware/
│   │   ├── auth.js
│   │   └── upload.js
│   ├── models/
│   │   ├── User.js
│   │   ├── Dish.js
│   │   ├── Order.js
│   │   ├── Reservation.js
│   │   └── Contact.js
│   ├── routes/
│   │   ├── auth.js
│   │   ├── menu.js
│   │   ├── orders.js
│   │   ├── reservations.js
│   │   └── contact.js
│   ├── scripts/
│   │   └── seed.js
│   ├── uploads/
│   ├── server.js
│   └── package.json
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   │   └── admin/
│   │   ├── context/
│   │   ├── styles/
│   │   └── utils/
│   └── package.json
└── README.md
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License.

## Support

For support, email <EMAIL> or create an issue in the repository.
