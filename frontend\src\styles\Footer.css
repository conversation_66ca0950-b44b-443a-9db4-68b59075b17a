.footer {
  background: #2c3e50;
  color: white;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-container:first-child {
  padding: 50px 20px 30px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.footer-section h3 {
  color: #e74c3c;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.footer-section h4 {
  color: #ecf0f1;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.footer-section p {
  color: #bdc3c7;
  line-height: 1.6;
  margin-bottom: 15px;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: #34495e;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  font-size: 18px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: #e74c3c;
  transform: translateY(-2px);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #e74c3c;
}

.contact-info p {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  color: #bdc3c7;
}

.hours p {
  margin-bottom: 8px;
  color: #bdc3c7;
}

.hours strong {
  color: #ecf0f1;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding: 20px 0;
}

.footer-bottom .footer-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.footer-bottom p {
  color: #bdc3c7;
  margin: 0;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-links a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-container:first-child {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    padding: 40px 20px 20px;
  }

  .footer-section {
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }

  .footer-bottom .footer-container {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-container:first-child {
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 30px 15px 15px;
  }

  .footer-section h3 {
    font-size: 1.1rem;
  }

  .footer-section h4 {
    font-size: 1rem;
  }

  .social-links a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    font-size: 16px;
  }

  .footer-links {
    flex-direction: column;
    gap: 10px;
  }

  .contact-info p {
    justify-content: center;
  }
}
