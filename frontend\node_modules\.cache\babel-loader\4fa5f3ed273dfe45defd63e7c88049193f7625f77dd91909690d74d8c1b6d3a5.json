{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Restaurant App\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  adminOnly = false\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 12\n    }, this);\n  }\n  if (adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"7q0flPEpi0wJibJzdagPdtYRDq8=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "adminOnly", "_s", "isAuthenticated", "user", "loading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "role", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst ProtectedRoute = ({ children, adminOnly = false }) => {\n  const { isAuthenticated, user, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <div className=\"loading-spinner\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (adminOnly && user?.role !== 'admin') {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGT,OAAO,CAAC,CAAC;EAEpD,IAAIS,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKQ,SAAS,EAAC,mBAAmB;MAAAN,QAAA,eAChCF,OAAA;QAAKQ,SAAS,EAAC,iBAAiB;QAAAN,QAAA,EAAC;MAAU;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC;EAEV;EAEA,IAAI,CAACP,eAAe,EAAE;IACpB,oBAAOL,OAAA,CAACH,QAAQ;MAACgB,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,IAAIT,SAAS,IAAI,CAAAG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,MAAK,OAAO,EAAE;IACvC,oBAAOf,OAAA,CAACH,QAAQ;MAACgB,EAAE,EAAC,GAAG;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpC;EAEA,OAAOV,QAAQ;AACjB,CAAC;AAACE,EAAA,CApBIH,cAAc;EAAA,QACyBH,OAAO;AAAA;AAAAkB,EAAA,GAD9Cf,cAAc;AAsBpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}