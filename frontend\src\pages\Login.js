import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import '../styles/Auth.css';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);

  const { login, isAuthenticated, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const from = location.state?.from?.pathname || '/';

  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const result = await login(formData);
    if (result.success) {
      navigate(from, { replace: true });
    }
  };

  return (
    <div className="auth-page">
      <div className="auth-container">
        <div className="auth-form">
          <div className="auth-header">
            <h2>Welcome Back</h2>
            <p>Sign in to your account to continue</p>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="email">Email Address</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                placeholder="Enter your email"
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">Password</label>
              <div className="password-input">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? '👁️' : '👁️‍🗨️'}
                </button>
              </div>
            </div>

            <button 
              type="submit" 
              className="auth-btn"
              disabled={loading}
            >
              {loading ? 'Signing In...' : 'Sign In'}
            </button>
          </form>

          <div className="auth-footer">
            <p>
              Don't have an account? 
              <Link to="/register" state={{ from: location.state?.from }}>
                Sign up here
              </Link>
            </p>
          </div>

          {/* Demo Accounts */}
          <div className="demo-accounts">
            <h4>Demo Accounts</h4>
            <div className="demo-buttons">
              <button
                type="button"
                className="demo-btn"
                onClick={() => setFormData({
                  email: '<EMAIL>',
                  password: 'admin123'
                })}
              >
                Admin Demo
              </button>
              <button
                type="button"
                className="demo-btn"
                onClick={() => setFormData({
                  email: '<EMAIL>',
                  password: 'customer123'
                })}
              >
                Customer Demo
              </button>
            </div>
          </div>
        </div>

        <div className="auth-image">
          <img src="/api/placeholder/500/600" alt="Restaurant ambiance" />
          <div className="auth-image-overlay">
            <h3>Delicious Restaurant</h3>
            <p>Experience the finest dining with our carefully crafted dishes</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
