import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

const ReservationManagement = () => {
  const [reservations, setReservations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: 'all',
    date: ''
  });
  const [selectedReservation, setSelectedReservation] = useState(null);

  useEffect(() => {
    fetchReservations();
  }, [filters]);

  const fetchReservations = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.status !== 'all') params.append('status', filters.status);
      if (filters.date) params.append('date', filters.date);
      
      const response = await axios.get(`/api/reservations/admin/all?${params}`);
      setReservations(response.data.reservations);
    } catch (error) {
      toast.error('Failed to load reservations');
    } finally {
      setLoading(false);
    }
  };

  const updateReservationStatus = async (reservationId, newStatus, tableNumber = null) => {
    try {
      const updateData = { status: newStatus };
      if (tableNumber) updateData.tableNumber = tableNumber;
      
      await axios.put(`/api/reservations/${reservationId}/status`, updateData);
      toast.success('Reservation status updated successfully!');
      fetchReservations();
      if (selectedReservation && selectedReservation._id === reservationId) {
        setSelectedReservation({ 
          ...selectedReservation, 
          status: newStatus,
          tableNumber: tableNumber || selectedReservation.tableNumber
        });
      }
    } catch (error) {
      toast.error('Failed to update reservation status');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: '#f39c12',
      confirmed: '#3498db',
      seated: '#e67e22',
      completed: '#27ae60',
      cancelled: '#e74c3c',
      'no-show': '#95a5a6'
    };
    return colors[status] || '#95a5a6';
  };

  const handleTableAssignment = (reservationId) => {
    const tableNumber = prompt('Enter table number:');
    if (tableNumber && !isNaN(tableNumber)) {
      updateReservationStatus(reservationId, 'confirmed', parseInt(tableNumber));
    }
  };

  if (loading) {
    return <div className="loading">Loading reservations...</div>;
  }

  return (
    <div className="reservation-management">
      <div className="container">
        <div className="page-header">
          <h1>Reservation Management</h1>
          <div className="filters">
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="seated">Seated</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="no-show">No Show</option>
            </select>
            <input
              type="date"
              value={filters.date}
              onChange={(e) => setFilters({ ...filters, date: e.target.value })}
            />
          </div>
        </div>

        <div className="reservations-layout">
          <div className="reservations-list">
            {reservations.length === 0 ? (
              <div className="no-reservations">No reservations found</div>
            ) : (
              reservations.map((reservation) => (
                <div 
                  key={reservation._id} 
                  className={`reservation-item ${selectedReservation?._id === reservation._id ? 'selected' : ''}`}
                  onClick={() => setSelectedReservation(reservation)}
                >
                  <div className="reservation-header">
                    <h3>{reservation.customerName}</h3>
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(reservation.status) }}
                    >
                      {reservation.status}
                    </span>
                  </div>
                  <div className="reservation-info">
                    <p><strong>Date:</strong> {new Date(reservation.date).toLocaleDateString()}</p>
                    <p><strong>Time:</strong> {reservation.time}</p>
                    <p><strong>Party Size:</strong> {reservation.partySize}</p>
                    <p><strong>Phone:</strong> {reservation.customerPhone}</p>
                    {reservation.tableNumber && (
                      <p><strong>Table:</strong> {reservation.tableNumber}</p>
                    )}
                  </div>
                  <div className="reservation-actions">
                    {reservation.status === 'pending' && (
                      <>
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleTableAssignment(reservation._id);
                          }}
                        >
                          Confirm & Assign Table
                        </button>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            updateReservationStatus(reservation._id, 'cancelled');
                          }}
                        >
                          Cancel
                        </button>
                      </>
                    )}
                    {reservation.status === 'confirmed' && (
                      <button
                        className="btn btn-primary btn-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          updateReservationStatus(reservation._id, 'seated');
                        }}
                      >
                        Mark as Seated
                      </button>
                    )}
                    {reservation.status === 'seated' && (
                      <button
                        className="btn btn-primary btn-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          updateReservationStatus(reservation._id, 'completed');
                        }}
                      >
                        Complete
                      </button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {selectedReservation && (
            <div className="reservation-details">
              <div className="reservation-details-header">
                <h2>Reservation Details</h2>
                <button 
                  className="close-btn"
                  onClick={() => setSelectedReservation(null)}
                >
                  ×
                </button>
              </div>

              <div className="reservation-details-content">
                <div className="detail-section">
                  <h3>Reservation Information</h3>
                  <p><strong>Confirmation Number:</strong> {selectedReservation.confirmationNumber}</p>
                  <p><strong>Status:</strong> 
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(selectedReservation.status) }}
                    >
                      {selectedReservation.status}
                    </span>
                  </p>
                  <p><strong>Date:</strong> {new Date(selectedReservation.date).toLocaleDateString()}</p>
                  <p><strong>Time:</strong> {selectedReservation.time}</p>
                  <p><strong>Party Size:</strong> {selectedReservation.partySize}</p>
                  <p><strong>Occasion:</strong> {selectedReservation.occasion}</p>
                  {selectedReservation.tableNumber && (
                    <p><strong>Table Number:</strong> {selectedReservation.tableNumber}</p>
                  )}
                  <p><strong>Created:</strong> {new Date(selectedReservation.createdAt).toLocaleString()}</p>
                </div>

                <div className="detail-section">
                  <h3>Customer Information</h3>
                  <p><strong>Name:</strong> {selectedReservation.customerName}</p>
                  <p><strong>Email:</strong> {selectedReservation.customerEmail}</p>
                  <p><strong>Phone:</strong> {selectedReservation.customerPhone}</p>
                </div>

                {selectedReservation.specialRequests && (
                  <div className="detail-section">
                    <h3>Special Requests</h3>
                    <p>{selectedReservation.specialRequests}</p>
                  </div>
                )}

                <div className="detail-actions">
                  <h3>Update Status</h3>
                  <div className="status-buttons">
                    {['pending', 'confirmed', 'seated', 'completed', 'cancelled', 'no-show'].map((status) => (
                      <button
                        key={status}
                        className={`btn ${selectedReservation.status === status ? 'btn-primary' : 'btn-secondary'}`}
                        onClick={() => updateReservationStatus(selectedReservation._id, status)}
                        disabled={selectedReservation.status === status}
                      >
                        {status === 'no-show' ? 'No Show' : status.charAt(0).toUpperCase() + status.slice(1)}
                      </button>
                    ))}
                  </div>
                  
                  {selectedReservation.status === 'pending' && (
                    <div className="table-assignment">
                      <h4>Assign Table</h4>
                      <button
                        className="btn btn-primary"
                        onClick={() => handleTableAssignment(selectedReservation._id)}
                      >
                        Assign Table & Confirm
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReservationManagement;
