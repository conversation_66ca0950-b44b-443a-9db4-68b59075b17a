{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Restaurant App\\\\frontend\\\\src\\\\pages\\\\Reservations.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { toast } from 'react-toastify';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Reservations = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    customerName: (user === null || user === void 0 ? void 0 : user.name) || '',\n    customerEmail: (user === null || user === void 0 ? void 0 : user.email) || '',\n    customerPhone: (user === null || user === void 0 ? void 0 : user.phone) || '',\n    date: '',\n    time: '',\n    partySize: 2,\n    occasion: 'other',\n    specialRequests: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      await axios.post('/api/reservations', formData);\n      toast.success('Reservation request submitted successfully!');\n      setFormData({\n        ...formData,\n        date: '',\n        time: '',\n        partySize: 2,\n        occasion: 'other',\n        specialRequests: ''\n      });\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to submit reservation');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"reservations-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Make a Reservation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Book your table for an unforgettable dining experience\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reservation-form-container\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"reservation-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"customerName\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"customerName\",\n                name: \"customerName\",\n                value: formData.customerName,\n                onChange: handleChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"customerEmail\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"customerEmail\",\n                name: \"customerEmail\",\n                value: formData.customerEmail,\n                onChange: handleChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"customerPhone\",\n                children: \"Phone Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                id: \"customerPhone\",\n                name: \"customerPhone\",\n                value: formData.customerPhone,\n                onChange: handleChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"partySize\",\n                children: \"Party Size\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"partySize\",\n                name: \"partySize\",\n                value: formData.partySize,\n                onChange: handleChange,\n                required: true,\n                children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: i + 1,\n                  children: [i + 1, \" \", i === 0 ? 'Person' : 'People']\n                }, i + 1, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"date\",\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                id: \"date\",\n                name: \"date\",\n                value: formData.date,\n                onChange: handleChange,\n                min: new Date().toISOString().split('T')[0],\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"time\",\n                children: \"Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"time\",\n                name: \"time\",\n                value: formData.time,\n                onChange: handleChange,\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11:00\",\n                  children: \"11:00 AM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11:30\",\n                  children: \"11:30 AM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12:00\",\n                  children: \"12:00 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12:30\",\n                  children: \"12:30 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"13:00\",\n                  children: \"1:00 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"13:30\",\n                  children: \"1:30 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"14:00\",\n                  children: \"2:00 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"14:30\",\n                  children: \"2:30 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"17:00\",\n                  children: \"5:00 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"17:30\",\n                  children: \"5:30 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"18:00\",\n                  children: \"6:00 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"18:30\",\n                  children: \"6:30 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"19:00\",\n                  children: \"7:00 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"19:30\",\n                  children: \"7:30 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"20:00\",\n                  children: \"8:00 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"20:30\",\n                  children: \"8:30 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"21:00\",\n                  children: \"9:00 PM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"occasion\",\n              children: \"Occasion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"occasion\",\n              name: \"occasion\",\n              value: formData.occasion,\n              onChange: handleChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"other\",\n                children: \"General Dining\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"birthday\",\n                children: \"Birthday\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"anniversary\",\n                children: \"Anniversary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"business\",\n                children: \"Business Meeting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"date\",\n                children: \"Date Night\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"family\",\n                children: \"Family Gathering\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"specialRequests\",\n              children: \"Special Requests\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"specialRequests\",\n              name: \"specialRequests\",\n              value: formData.specialRequests,\n              onChange: handleChange,\n              rows: \"4\",\n              placeholder: \"Any special requests or dietary requirements...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            disabled: loading,\n            children: loading ? 'Submitting...' : 'Make Reservation'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(Reservations, \"9CPuazBhIpiEHsfJRtdUCSrAwxg=\", false, function () {\n  return [useAuth];\n});\n_c = Reservations;\nexport default Reservations;\nvar _c;\n$RefreshReg$(_c, \"Reservations\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "toast", "axios", "jsxDEV", "_jsxDEV", "Reservations", "_s", "user", "formData", "setFormData", "customerName", "name", "customerEmail", "email", "customerPhone", "phone", "date", "time", "partySize", "occasion", "specialRequests", "loading", "setLoading", "handleChange", "e", "target", "value", "handleSubmit", "preventDefault", "post", "success", "error", "_error$response", "_error$response$data", "response", "data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "Array", "map", "_", "i", "min", "Date", "toISOString", "split", "rows", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/src/pages/Reservations.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { toast } from 'react-toastify';\nimport axios from 'axios';\n\nconst Reservations = () => {\n  const { user } = useAuth();\n  const [formData, setFormData] = useState({\n    customerName: user?.name || '',\n    customerEmail: user?.email || '',\n    customerPhone: user?.phone || '',\n    date: '',\n    time: '',\n    partySize: 2,\n    occasion: 'other',\n    specialRequests: ''\n  });\n  const [loading, setLoading] = useState(false);\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      await axios.post('/api/reservations', formData);\n      toast.success('Reservation request submitted successfully!');\n      setFormData({\n        ...formData,\n        date: '',\n        time: '',\n        partySize: 2,\n        occasion: 'other',\n        specialRequests: ''\n      });\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Failed to submit reservation');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"reservations-page\">\n      <div className=\"container\">\n        <div className=\"page-header\">\n          <h1>Make a Reservation</h1>\n          <p>Book your table for an unforgettable dining experience</p>\n        </div>\n\n        <div className=\"reservation-form-container\">\n          <form onSubmit={handleSubmit} className=\"reservation-form\">\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"customerName\">Full Name</label>\n                <input\n                  type=\"text\"\n                  id=\"customerName\"\n                  name=\"customerName\"\n                  value={formData.customerName}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"customerEmail\">Email</label>\n                <input\n                  type=\"email\"\n                  id=\"customerEmail\"\n                  name=\"customerEmail\"\n                  value={formData.customerEmail}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"customerPhone\">Phone Number</label>\n                <input\n                  type=\"tel\"\n                  id=\"customerPhone\"\n                  name=\"customerPhone\"\n                  value={formData.customerPhone}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"partySize\">Party Size</label>\n                <select\n                  id=\"partySize\"\n                  name=\"partySize\"\n                  value={formData.partySize}\n                  onChange={handleChange}\n                  required\n                >\n                  {[...Array(20)].map((_, i) => (\n                    <option key={i + 1} value={i + 1}>\n                      {i + 1} {i === 0 ? 'Person' : 'People'}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"date\">Date</label>\n                <input\n                  type=\"date\"\n                  id=\"date\"\n                  name=\"date\"\n                  value={formData.date}\n                  onChange={handleChange}\n                  min={new Date().toISOString().split('T')[0]}\n                  required\n                />\n              </div>\n              <div className=\"form-group\">\n                <label htmlFor=\"time\">Time</label>\n                <select\n                  id=\"time\"\n                  name=\"time\"\n                  value={formData.time}\n                  onChange={handleChange}\n                  required\n                >\n                  <option value=\"\">Select Time</option>\n                  <option value=\"11:00\">11:00 AM</option>\n                  <option value=\"11:30\">11:30 AM</option>\n                  <option value=\"12:00\">12:00 PM</option>\n                  <option value=\"12:30\">12:30 PM</option>\n                  <option value=\"13:00\">1:00 PM</option>\n                  <option value=\"13:30\">1:30 PM</option>\n                  <option value=\"14:00\">2:00 PM</option>\n                  <option value=\"14:30\">2:30 PM</option>\n                  <option value=\"17:00\">5:00 PM</option>\n                  <option value=\"17:30\">5:30 PM</option>\n                  <option value=\"18:00\">6:00 PM</option>\n                  <option value=\"18:30\">6:30 PM</option>\n                  <option value=\"19:00\">7:00 PM</option>\n                  <option value=\"19:30\">7:30 PM</option>\n                  <option value=\"20:00\">8:00 PM</option>\n                  <option value=\"20:30\">8:30 PM</option>\n                  <option value=\"21:00\">9:00 PM</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"occasion\">Occasion</label>\n              <select\n                id=\"occasion\"\n                name=\"occasion\"\n                value={formData.occasion}\n                onChange={handleChange}\n              >\n                <option value=\"other\">General Dining</option>\n                <option value=\"birthday\">Birthday</option>\n                <option value=\"anniversary\">Anniversary</option>\n                <option value=\"business\">Business Meeting</option>\n                <option value=\"date\">Date Night</option>\n                <option value=\"family\">Family Gathering</option>\n              </select>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"specialRequests\">Special Requests</label>\n              <textarea\n                id=\"specialRequests\"\n                name=\"specialRequests\"\n                value={formData.specialRequests}\n                onChange={handleChange}\n                rows=\"4\"\n                placeholder=\"Any special requests or dietary requirements...\"\n              />\n            </div>\n\n            <button type=\"submit\" className=\"btn btn-primary\" disabled={loading}>\n              {loading ? 'Submitting...' : 'Make Reservation'}\n            </button>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Reservations;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,YAAY,EAAE,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,IAAI,KAAI,EAAE;IAC9BC,aAAa,EAAE,CAAAL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,KAAK,KAAI,EAAE;IAChCC,aAAa,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,KAAK,KAAI,EAAE;IAChCC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,OAAO;IACjBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMwB,YAAY,GAAIC,CAAC,IAAK;IAC1Bf,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACgB,CAAC,CAACC,MAAM,CAACd,IAAI,GAAGa,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBN,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMpB,KAAK,CAAC2B,IAAI,CAAC,mBAAmB,EAAErB,QAAQ,CAAC;MAC/CP,KAAK,CAAC6B,OAAO,CAAC,6CAA6C,CAAC;MAC5DrB,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXQ,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,OAAO;QACjBC,eAAe,EAAE;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOW,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdhC,KAAK,CAAC8B,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACG,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI,8BAA8B,CAAC;IAC9E,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACElB,OAAA;IAAKiC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChClC,OAAA;MAAKiC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlC,OAAA;QAAKiC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlC,OAAA;UAAAkC,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BtC,OAAA;UAAAkC,QAAA,EAAG;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAENtC,OAAA;QAAKiC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzClC,OAAA;UAAMuC,QAAQ,EAAEhB,YAAa;UAACU,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBACxDlC,OAAA;YAAKiC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlC,OAAA;cAAKiC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlC,OAAA;gBAAOwC,OAAO,EAAC,cAAc;gBAAAN,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/CtC,OAAA;gBACEyC,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,cAAc;gBACjBnC,IAAI,EAAC,cAAc;gBACnBe,KAAK,EAAElB,QAAQ,CAACE,YAAa;gBAC7BqC,QAAQ,EAAExB,YAAa;gBACvByB,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlC,OAAA;gBAAOwC,OAAO,EAAC,eAAe;gBAAAN,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5CtC,OAAA;gBACEyC,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAC,eAAe;gBAClBnC,IAAI,EAAC,eAAe;gBACpBe,KAAK,EAAElB,QAAQ,CAACI,aAAc;gBAC9BmC,QAAQ,EAAExB,YAAa;gBACvByB,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlC,OAAA;cAAKiC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlC,OAAA;gBAAOwC,OAAO,EAAC,eAAe;gBAAAN,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnDtC,OAAA;gBACEyC,IAAI,EAAC,KAAK;gBACVC,EAAE,EAAC,eAAe;gBAClBnC,IAAI,EAAC,eAAe;gBACpBe,KAAK,EAAElB,QAAQ,CAACM,aAAc;gBAC9BiC,QAAQ,EAAExB,YAAa;gBACvByB,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlC,OAAA;gBAAOwC,OAAO,EAAC,WAAW;gBAAAN,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7CtC,OAAA;gBACE0C,EAAE,EAAC,WAAW;gBACdnC,IAAI,EAAC,WAAW;gBAChBe,KAAK,EAAElB,QAAQ,CAACU,SAAU;gBAC1B6B,QAAQ,EAAExB,YAAa;gBACvByB,QAAQ;gBAAAV,QAAA,EAEP,CAAC,GAAGW,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvBhD,OAAA;kBAAoBsB,KAAK,EAAE0B,CAAC,GAAG,CAAE;kBAAAd,QAAA,GAC9Bc,CAAC,GAAG,CAAC,EAAC,GAAC,EAACA,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,QAAQ;gBAAA,GAD3BA,CAAC,GAAG,CAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlC,OAAA;cAAKiC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlC,OAAA;gBAAOwC,OAAO,EAAC,MAAM;gBAAAN,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClCtC,OAAA;gBACEyC,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,MAAM;gBACTnC,IAAI,EAAC,MAAM;gBACXe,KAAK,EAAElB,QAAQ,CAACQ,IAAK;gBACrB+B,QAAQ,EAAExB,YAAa;gBACvB8B,GAAG,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;gBAC5CR,QAAQ;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlC,OAAA;gBAAOwC,OAAO,EAAC,MAAM;gBAAAN,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClCtC,OAAA;gBACE0C,EAAE,EAAC,MAAM;gBACTnC,IAAI,EAAC,MAAM;gBACXe,KAAK,EAAElB,QAAQ,CAACS,IAAK;gBACrB8B,QAAQ,EAAExB,YAAa;gBACvByB,QAAQ;gBAAAV,QAAA,gBAERlC,OAAA;kBAAQsB,KAAK,EAAC,EAAE;kBAAAY,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtC,OAAA;kBAAQsB,KAAK,EAAC,OAAO;kBAAAY,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlC,OAAA;cAAOwC,OAAO,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CtC,OAAA;cACE0C,EAAE,EAAC,UAAU;cACbnC,IAAI,EAAC,UAAU;cACfe,KAAK,EAAElB,QAAQ,CAACW,QAAS;cACzB4B,QAAQ,EAAExB,YAAa;cAAAe,QAAA,gBAEvBlC,OAAA;gBAAQsB,KAAK,EAAC,OAAO;gBAAAY,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7CtC,OAAA;gBAAQsB,KAAK,EAAC,UAAU;gBAAAY,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CtC,OAAA;gBAAQsB,KAAK,EAAC,aAAa;gBAAAY,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDtC,OAAA;gBAAQsB,KAAK,EAAC,UAAU;gBAAAY,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDtC,OAAA;gBAAQsB,KAAK,EAAC,MAAM;gBAAAY,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCtC,OAAA;gBAAQsB,KAAK,EAAC,QAAQ;gBAAAY,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlC,OAAA;cAAOwC,OAAO,EAAC,iBAAiB;cAAAN,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzDtC,OAAA;cACE0C,EAAE,EAAC,iBAAiB;cACpBnC,IAAI,EAAC,iBAAiB;cACtBe,KAAK,EAAElB,QAAQ,CAACY,eAAgB;cAChC2B,QAAQ,EAAExB,YAAa;cACvBkC,IAAI,EAAC,GAAG;cACRC,WAAW,EAAC;YAAiD;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtC,OAAA;YAAQyC,IAAI,EAAC,QAAQ;YAACR,SAAS,EAAC,iBAAiB;YAACsB,QAAQ,EAAEtC,OAAQ;YAAAiB,QAAA,EACjEjB,OAAO,GAAG,eAAe,GAAG;UAAkB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpC,EAAA,CA7LID,YAAY;EAAA,QACCL,OAAO;AAAA;AAAA4D,EAAA,GADpBvD,YAAY;AA+LlB,eAAeA,YAAY;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}