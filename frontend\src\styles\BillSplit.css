.bill-split-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.bill-split-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.bill-split-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.bill-split-header h2 {
  color: #2c3e50;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e9ecef;
  color: #2c3e50;
}

/* Split Summary */
.split-summary {
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-info h3 {
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.total-info p {
  margin: 5px 0;
  color: #7f8c8d;
}

.share-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.share-btn:hover {
  background: #2980b9;
}

/* Participants List */
.participants-list {
  padding: 20px;
}

.participants-list h3 {
  color: #2c3e50;
  margin: 0 0 20px 0;
}

.participant-card {
  border: 2px solid #eee;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.participant-info h4 {
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.participant-info p {
  margin: 2px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.status.pending {
  background: #f39c12;
  color: white;
}

.status.paid {
  background: #27ae60;
  color: white;
}

.status.failed {
  background: #e74c3c;
  color: white;
}

.payment-methods {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.payment-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.payment-btn.cash {
  background: #27ae60;
  color: white;
}

.payment-btn.cash:hover {
  background: #229954;
}

.payment-btn.card {
  background: #3498db;
  color: white;
}

.payment-btn.card:hover {
  background: #2980b9;
}

.paid-info {
  color: #27ae60;
  font-size: 14px;
}

/* Split Setup */
.split-setup {
  padding: 20px;
}

.order-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.order-summary h3 {
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #ddd;
}

.item:last-child {
  border-bottom: none;
}

.split-options {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #2c3e50;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: #e74c3c;
}

.additional-costs {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.additional-costs h4 {
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.cost-inputs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

/* Participants Setup */
.participants-setup h3 {
  color: #2c3e50;
  margin: 0 0 20px 0;
}

.participant-form {
  border: 2px solid #eee;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.participant-basic {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.custom-amount {
  margin-bottom: 15px;
}

.item-assignment h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 14px;
}

.item-assign {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.item-assign:last-child {
  border-bottom: none;
}

.item-assign input {
  width: 60px;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
}

.calculated-amount {
  background: #e8f5e8;
  padding: 10px;
  border-radius: 6px;
  text-align: center;
  color: #27ae60;
}

/* Split Actions */
.split-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-primary {
  background: #e74c3c;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #c0392b;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .bill-split-modal {
    padding: 10px;
  }

  .split-summary {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .participant-card {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .payment-methods {
    justify-content: center;
  }

  .participant-basic {
    grid-template-columns: 1fr;
  }

  .cost-inputs {
    grid-template-columns: 1fr;
  }

  .split-actions {
    flex-direction: column;
  }
}
