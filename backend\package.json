{"name": "restaurant-backend", "version": "1.0.0", "description": "Backend API for Restaurant Website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["restaurant", "mern", "api"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0", "multer": "^2.0.0", "nodemailer": "^7.0.3", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}