[{"D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\index.js": "1", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\App.js": "2", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\reportWebVitals.js": "3", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Home.js": "4", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\AuthContext.js": "5", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\CartContext.js": "6", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Reservations.js": "7", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Menu.js": "8", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Login.js": "9", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Profile.js": "10", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Register.js": "11", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Contact.js": "12", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Orders.js": "13", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Footer.js": "14", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\ProtectedRoute.js": "15", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Navbar.js": "16", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\OrderManagement.js": "17", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\MenuManagement.js": "18", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\Dashboard.js": "19", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ContactManagement.js": "20", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ReservationManagement.js": "21", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Cart.js": "22"}, {"size": 535, "mtime": 1748922294881, "results": "23", "hashOfConfig": "24"}, {"size": 3811, "mtime": 1748922443277, "results": "25", "hashOfConfig": "24"}, {"size": 362, "mtime": 1748922295292, "results": "26", "hashOfConfig": "24"}, {"size": 5829, "mtime": 1748922548807, "results": "27", "hashOfConfig": "24"}, {"size": 4735, "mtime": 1748922463633, "results": "28", "hashOfConfig": "24"}, {"size": 4038, "mtime": 1748922479043, "results": "29", "hashOfConfig": "24"}, {"size": 6709, "mtime": 1748922668933, "results": "30", "hashOfConfig": "24"}, {"size": 8892, "mtime": 1748922575495, "results": "31", "hashOfConfig": "24"}, {"size": 4167, "mtime": 1748922589856, "results": "32", "hashOfConfig": "24"}, {"size": 4241, "mtime": 1748922700168, "results": "33", "hashOfConfig": "24"}, {"size": 8058, "mtime": 1748922611812, "results": "34", "hashOfConfig": "24"}, {"size": 5040, "mtime": 1748922685415, "results": "35", "hashOfConfig": "24"}, {"size": 3320, "mtime": 1748922713689, "results": "36", "hashOfConfig": "24"}, {"size": 2130, "mtime": 1748922501061, "results": "37", "hashOfConfig": "24"}, {"size": 617, "mtime": 1748922507062, "results": "38", "hashOfConfig": "24"}, {"size": 3156, "mtime": 1748922490906, "results": "39", "hashOfConfig": "24"}, {"size": 9955, "mtime": 1748923154750, "results": "40", "hashOfConfig": "24"}, {"size": 11776, "mtime": 1748923126375, "results": "41", "hashOfConfig": "24"}, {"size": 4691, "mtime": 1748922731563, "results": "42", "hashOfConfig": "24"}, {"size": 12323, "mtime": 1748923210709, "results": "43", "hashOfConfig": "24"}, {"size": 10586, "mtime": 1748923181786, "results": "44", "hashOfConfig": "24"}, {"size": 8555, "mtime": 1748922527834, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "113lfpm", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\index.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\App.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\reportWebVitals.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Home.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\AuthContext.js", ["112"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\CartContext.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Reservations.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Menu.js", ["113"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Login.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Profile.js", ["114"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Register.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Contact.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Orders.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Footer.js", ["115", "116", "117", "118"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Navbar.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\OrderManagement.js", ["119"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\MenuManagement.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\Dashboard.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ContactManagement.js", ["120"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ReservationManagement.js", ["121"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Cart.js", ["122"], [], {"ruleId": "123", "severity": 1, "message": "124", "line": 178, "column": 6, "nodeType": "125", "endLine": 178, "endColumn": 8, "suggestions": "126"}, {"ruleId": "123", "severity": 1, "message": "127", "line": 35, "column": 6, "nodeType": "125", "endLine": 35, "endColumn": 32, "suggestions": "128"}, {"ruleId": "129", "severity": 1, "message": "130", "line": 43, "column": 11, "nodeType": "131", "messageId": "132", "endLine": 43, "endColumn": 17}, {"ruleId": "133", "severity": 1, "message": "134", "line": 13, "column": 13, "nodeType": "135", "endLine": 13, "endColumn": 47}, {"ruleId": "133", "severity": 1, "message": "134", "line": 14, "column": 13, "nodeType": "135", "endLine": 14, "endColumn": 48}, {"ruleId": "133", "severity": 1, "message": "134", "line": 15, "column": 13, "nodeType": "135", "endLine": 15, "endColumn": 46}, {"ruleId": "133", "severity": 1, "message": "134", "line": 16, "column": 13, "nodeType": "135", "endLine": 16, "endColumn": 46}, {"ruleId": "123", "severity": 1, "message": "136", "line": 16, "column": 6, "nodeType": "125", "endLine": 16, "endColumn": 15, "suggestions": "137"}, {"ruleId": "123", "severity": 1, "message": "138", "line": 18, "column": 6, "nodeType": "125", "endLine": 18, "endColumn": 15, "suggestions": "139"}, {"ruleId": "123", "severity": 1, "message": "140", "line": 16, "column": 6, "nodeType": "125", "endLine": 16, "endColumn": 15, "suggestions": "141"}, {"ruleId": "129", "severity": 1, "message": "142", "line": 70, "column": 13, "nodeType": "131", "messageId": "132", "endLine": 70, "endColumn": 21}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUser'. Either include it or remove the dependency array.", "ArrayExpression", ["143"], "React Hook useEffect has a missing dependency: 'fetchDishes'. Either include it or remove the dependency array.", ["144"], "no-unused-vars", "'result' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["145"], "React Hook useEffect has a missing dependency: 'fetchContacts'. Either include it or remove the dependency array.", ["146"], "React Hook useEffect has a missing dependency: 'fetchReservations'. Either include it or remove the dependency array.", ["147"], "'response' is assigned a value but never used.", {"desc": "148", "fix": "149"}, {"desc": "150", "fix": "151"}, {"desc": "152", "fix": "153"}, {"desc": "154", "fix": "155"}, {"desc": "156", "fix": "157"}, "Update the dependencies array to be: [loadUser]", {"range": "158", "text": "159"}, "Update the dependencies array to be: [fetchDishes, filters, pagination.page]", {"range": "160", "text": "161"}, "Update the dependencies array to be: [fetchOrders, filters]", {"range": "162", "text": "163"}, "Update the dependencies array to be: [fetchContacts, filters]", {"range": "164", "text": "165"}, "Update the dependencies array to be: [fetchReservations, filters]", {"range": "166", "text": "167"}, [4282, 4284], "[loadUser]", [806, 832], "[fetchDishes, filters, pagination.page]", [438, 447], "[fetchOrders, filters]", [566, 575], "[fetchContacts, filters]", [466, 475], "[fetchReservations, filters]"]