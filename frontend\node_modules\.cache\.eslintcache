[{"D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\index.js": "1", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\App.js": "2", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\reportWebVitals.js": "3", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Home.js": "4", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\AuthContext.js": "5", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\CartContext.js": "6", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Reservations.js": "7", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Menu.js": "8", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Login.js": "9", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Profile.js": "10", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Register.js": "11", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Contact.js": "12", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Orders.js": "13", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Footer.js": "14", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\ProtectedRoute.js": "15", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Navbar.js": "16", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\OrderManagement.js": "17", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\MenuManagement.js": "18", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\Dashboard.js": "19", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ContactManagement.js": "20", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ReservationManagement.js": "21", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Cart.js": "22", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\BillSplit.js": "23", "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\BillSplitPage.js": "24"}, {"size": 535, "mtime": 1748922294881, "results": "25", "hashOfConfig": "26"}, {"size": 3939, "mtime": 1748926017213, "results": "27", "hashOfConfig": "26"}, {"size": 362, "mtime": 1748922295292, "results": "28", "hashOfConfig": "26"}, {"size": 5829, "mtime": 1748922548807, "results": "29", "hashOfConfig": "26"}, {"size": 4735, "mtime": 1748922463633, "results": "30", "hashOfConfig": "26"}, {"size": 4038, "mtime": 1748922479043, "results": "31", "hashOfConfig": "26"}, {"size": 6709, "mtime": 1748922668933, "results": "32", "hashOfConfig": "26"}, {"size": 8892, "mtime": 1748922575495, "results": "33", "hashOfConfig": "26"}, {"size": 4167, "mtime": 1748922589856, "results": "34", "hashOfConfig": "26"}, {"size": 4241, "mtime": 1748922700168, "results": "35", "hashOfConfig": "26"}, {"size": 8058, "mtime": 1748922611812, "results": "36", "hashOfConfig": "26"}, {"size": 5040, "mtime": 1748922685415, "results": "37", "hashOfConfig": "26"}, {"size": 4920, "mtime": 1748925986846, "results": "38", "hashOfConfig": "26"}, {"size": 2130, "mtime": 1748922501061, "results": "39", "hashOfConfig": "26"}, {"size": 617, "mtime": 1748922507062, "results": "40", "hashOfConfig": "26"}, {"size": 3156, "mtime": 1748922490906, "results": "41", "hashOfConfig": "26"}, {"size": 9955, "mtime": 1748923154750, "results": "42", "hashOfConfig": "26"}, {"size": 11776, "mtime": 1748923126375, "results": "43", "hashOfConfig": "26"}, {"size": 4691, "mtime": 1748922731563, "results": "44", "hashOfConfig": "26"}, {"size": 12323, "mtime": 1748923210709, "results": "45", "hashOfConfig": "26"}, {"size": 10586, "mtime": 1748923181786, "results": "46", "hashOfConfig": "26"}, {"size": 8555, "mtime": 1748922527834, "results": "47", "hashOfConfig": "26"}, {"size": 13523, "mtime": 1748925907721, "results": "48", "hashOfConfig": "26"}, {"size": 10562, "mtime": 1748925939105, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "113lfpm", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\index.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\App.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\reportWebVitals.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Home.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\AuthContext.js", ["122"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\context\\CartContext.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Reservations.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Menu.js", ["123"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Login.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Profile.js", ["124"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Register.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Contact.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\Orders.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Footer.js", ["125", "126", "127", "128"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Navbar.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\OrderManagement.js", ["129"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\MenuManagement.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\Dashboard.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ContactManagement.js", ["130"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\admin\\ReservationManagement.js", ["131"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\Cart.js", ["132"], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\components\\BillSplit.js", [], [], "D:\\D Drive\\Projects\\Restaurant App\\frontend\\src\\pages\\BillSplitPage.js", ["133", "134", "135"], [], {"ruleId": "136", "severity": 1, "message": "137", "line": 178, "column": 6, "nodeType": "138", "endLine": 178, "endColumn": 8, "suggestions": "139"}, {"ruleId": "136", "severity": 1, "message": "140", "line": 35, "column": 6, "nodeType": "138", "endLine": 35, "endColumn": 32, "suggestions": "141"}, {"ruleId": "142", "severity": 1, "message": "143", "line": 43, "column": 11, "nodeType": "144", "messageId": "145", "endLine": 43, "endColumn": 17}, {"ruleId": "146", "severity": 1, "message": "147", "line": 13, "column": 13, "nodeType": "148", "endLine": 13, "endColumn": 47}, {"ruleId": "146", "severity": 1, "message": "147", "line": 14, "column": 13, "nodeType": "148", "endLine": 14, "endColumn": 48}, {"ruleId": "146", "severity": 1, "message": "147", "line": 15, "column": 13, "nodeType": "148", "endLine": 15, "endColumn": 46}, {"ruleId": "146", "severity": 1, "message": "147", "line": 16, "column": 13, "nodeType": "148", "endLine": 16, "endColumn": 46}, {"ruleId": "136", "severity": 1, "message": "149", "line": 16, "column": 6, "nodeType": "138", "endLine": 16, "endColumn": 15, "suggestions": "150"}, {"ruleId": "136", "severity": 1, "message": "151", "line": 18, "column": 6, "nodeType": "138", "endLine": 18, "endColumn": 15, "suggestions": "152"}, {"ruleId": "136", "severity": 1, "message": "153", "line": 16, "column": 6, "nodeType": "138", "endLine": 16, "endColumn": 15, "suggestions": "154"}, {"ruleId": "142", "severity": 1, "message": "155", "line": 70, "column": 13, "nodeType": "144", "messageId": "145", "endLine": 70, "endColumn": 21}, {"ruleId": "142", "severity": 1, "message": "156", "line": 11, "column": 10, "nodeType": "144", "messageId": "145", "endLine": 11, "endColumn": 29}, {"ruleId": "142", "severity": 1, "message": "157", "line": 11, "column": 31, "nodeType": "144", "messageId": "145", "endLine": 11, "endColumn": 53}, {"ruleId": "136", "severity": 1, "message": "158", "line": 16, "column": 6, "nodeType": "138", "endLine": 16, "endColumn": 15, "suggestions": "159"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUser'. Either include it or remove the dependency array.", "ArrayExpression", ["160"], "React Hook useEffect has a missing dependency: 'fetchDishes'. Either include it or remove the dependency array.", ["161"], "no-unused-vars", "'result' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["162"], "React Hook useEffect has a missing dependency: 'fetchContacts'. Either include it or remove the dependency array.", ["163"], "React Hook useEffect has a missing dependency: 'fetchReservations'. Either include it or remove the dependency array.", ["164"], "'response' is assigned a value but never used.", "'selectedParticipant' is assigned a value but never used.", "'setSelectedParticipant' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBillSplitData'. Either include it or remove the dependency array.", ["165"], {"desc": "166", "fix": "167"}, {"desc": "168", "fix": "169"}, {"desc": "170", "fix": "171"}, {"desc": "172", "fix": "173"}, {"desc": "174", "fix": "175"}, {"desc": "176", "fix": "177"}, "Update the dependencies array to be: [loadUser]", {"range": "178", "text": "179"}, "Update the dependencies array to be: [fetchDishes, filters, pagination.page]", {"range": "180", "text": "181"}, "Update the dependencies array to be: [fetchOrders, filters]", {"range": "182", "text": "183"}, "Update the dependencies array to be: [fetchContacts, filters]", {"range": "184", "text": "185"}, "Update the dependencies array to be: [fetchReservations, filters]", {"range": "186", "text": "187"}, "Update the dependencies array to be: [fetchBillSplitData, orderId]", {"range": "188", "text": "189"}, [4282, 4284], "[loadUser]", [806, 832], "[fetchDishes, filters, pagination.page]", [438, 447], "[fetchOrders, filters]", [566, 575], "[fetchContacts, filters]", [466, 475], "[fetchReservations, filters]", [553, 562], "[fetchBillSplitData, orderId]"]