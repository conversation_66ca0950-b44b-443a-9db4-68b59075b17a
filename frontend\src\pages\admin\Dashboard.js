import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalReservations: 0,
    totalMessages: 0,
    totalRevenue: 0
  });
  const [recentOrders, setRecentOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Fetch recent orders
      const ordersResponse = await axios.get('/api/orders/admin/all?limit=5');
      setRecentOrders(ordersResponse.data.orders);

      // Calculate basic stats (in a real app, you'd have dedicated endpoints)
      setStats({
        totalOrders: ordersResponse.data.pagination.total,
        totalReservations: 0, // Would fetch from reservations endpoint
        totalMessages: 0, // Would fetch from contacts endpoint
        totalRevenue: ordersResponse.data.orders.reduce((sum, order) => sum + order.totalAmount, 0)
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="loading">Loading dashboard...</div>;
  }

  return (
    <div className="admin-dashboard">
      <div className="container">
        <div className="dashboard-header">
          <h1>Admin Dashboard</h1>
          <p>Welcome to the restaurant management system</p>
        </div>

        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">📊</div>
            <div className="stat-info">
              <h3>{stats.totalOrders}</h3>
              <p>Total Orders</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">📅</div>
            <div className="stat-info">
              <h3>{stats.totalReservations}</h3>
              <p>Reservations</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">💬</div>
            <div className="stat-info">
              <h3>{stats.totalMessages}</h3>
              <p>Messages</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">💰</div>
            <div className="stat-info">
              <h3>${stats.totalRevenue.toFixed(2)}</h3>
              <p>Revenue</p>
            </div>
          </div>
        </div>

        <div className="dashboard-content">
          <div className="quick-actions">
            <h3>Quick Actions</h3>
            <div className="action-buttons">
              <Link to="/admin/menu" className="action-btn">
                <span>🍽️</span>
                Manage Menu
              </Link>
              <Link to="/admin/orders" className="action-btn">
                <span>📦</span>
                View Orders
              </Link>
              <Link to="/admin/reservations" className="action-btn">
                <span>📅</span>
                Reservations
              </Link>
              <Link to="/admin/contacts" className="action-btn">
                <span>💬</span>
                Messages
              </Link>
            </div>
          </div>

          <div className="recent-orders">
            <h3>Recent Orders</h3>
            {recentOrders.length === 0 ? (
              <p>No recent orders</p>
            ) : (
              <div className="orders-table">
                <table>
                  <thead>
                    <tr>
                      <th>Order #</th>
                      <th>Customer</th>
                      <th>Total</th>
                      <th>Status</th>
                      <th>Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentOrders.map((order) => (
                      <tr key={order._id}>
                        <td>{order.orderNumber}</td>
                        <td>{order.customer?.name}</td>
                        <td>${order.totalAmount}</td>
                        <td>
                          <span className={`status-badge ${order.status}`}>
                            {order.status}
                          </span>
                        </td>
                        <td>{new Date(order.createdAt).toLocaleDateString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
