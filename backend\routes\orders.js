const express = require('express');
const { body } = require('express-validator');
const {
  createOrder,
  getUserOrders,
  getOrder,
  getAllOrders,
  updateOrderStatus,
  cancelOrder,
  enableBillSplit,
  updateParticipantPayment,
  getBillSplitDetails
} = require('../controllers/orderController');
const { protect, admin } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const orderValidation = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),
  body('items.*.dish')
    .isMongoId()
    .withMessage('Invalid dish ID'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1'),
  body('orderType')
    .isIn(['delivery', 'pickup', 'dine-in'])
    .withMessage('Invalid order type'),
  body('paymentMethod')
    .isIn(['cash', 'card', 'online'])
    .withMessage('Invalid payment method'),
  body('deliveryAddress')
    .if(body('orderType').equals('delivery'))
    .notEmpty()
    .withMessage('Delivery address is required for delivery orders'),
  body('deliveryAddress.street')
    .if(body('orderType').equals('delivery'))
    .notEmpty()
    .withMessage('Street address is required'),
  body('deliveryAddress.city')
    .if(body('orderType').equals('delivery'))
    .notEmpty()
    .withMessage('City is required'),
  body('deliveryAddress.phone')
    .if(body('orderType').equals('delivery'))
    .matches(/^\+?[\d\s-()]+$/)
    .withMessage('Valid phone number is required for delivery')
];

const statusValidation = [
  body('status')
    .isIn(['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'])
    .withMessage('Invalid status')
];

// User routes
router.post('/', protect, orderValidation, createOrder);
router.get('/', protect, getUserOrders);
router.get('/:id', protect, getOrder);
router.put('/:id/cancel', protect, cancelOrder);

// Bill splitting routes
router.post('/:id/split', protect, enableBillSplit);
router.get('/:id/split', getBillSplitDetails);
router.put('/:id/split/participant/:participantId', updateParticipantPayment);

// Admin routes
router.get('/admin/all', protect, admin, getAllOrders);
router.put('/:id/status', protect, admin, statusValidation, updateOrderStatus);

module.exports = router;
