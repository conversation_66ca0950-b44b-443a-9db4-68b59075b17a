{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\nimport transitionalDefaults from './transitional.js';\nimport toFormData from '../helpers/toFormData.js';\nimport toURLEncodedForm from '../helpers/toURLEncodedForm.js';\nimport platform from '../platform/index.js';\nimport formDataToJSON from '../helpers/formDataToJSON.js';\n\n/**\n * It takes a string, tries to parse it, and if it fails, it returns the stringified version\n * of the input\n *\n * @param {any} rawValue - The value to be stringified.\n * @param {Function} parser - A function that parses a string into a JavaScript object.\n * @param {Function} encoder - A function that takes a value and returns a string.\n *\n * @returns {string} A stringified version of the rawValue.\n */\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n  return (encoder || JSON.stringify)(rawValue);\n}\nconst defaults = {\n  transitional: transitionalDefaults,\n  adapter: ['xhr', 'http', 'fetch'],\n  transformRequest: [function transformRequest(data, headers) {\n    const contentType = headers.getContentType() || '';\n    const hasJSONContentType = contentType.indexOf('application/json') > -1;\n    const isObjectPayload = utils.isObject(data);\n    if (isObjectPayload && utils.isHTMLForm(data)) {\n      data = new FormData(data);\n    }\n    const isFormData = utils.isFormData(data);\n    if (isFormData) {\n      return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;\n    }\n    if (utils.isArrayBuffer(data) || utils.isBuffer(data) || utils.isStream(data) || utils.isFile(data) || utils.isBlob(data) || utils.isReadableStream(data)) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      headers.setContentType('application/x-www-form-urlencoded;charset=utf-8', false);\n      return data.toString();\n    }\n    let isFileList;\n    if (isObjectPayload) {\n      if (contentType.indexOf('application/x-www-form-urlencoded') > -1) {\n        return toURLEncodedForm(data, this.formSerializer).toString();\n      }\n      if ((isFileList = utils.isFileList(data)) || contentType.indexOf('multipart/form-data') > -1) {\n        const _FormData = this.env && this.env.FormData;\n        return toFormData(isFileList ? {\n          'files[]': data\n        } : data, _FormData && new _FormData(), this.formSerializer);\n      }\n    }\n    if (isObjectPayload || hasJSONContentType) {\n      headers.setContentType('application/json', false);\n      return stringifySafely(data);\n    }\n    return data;\n  }],\n  transformResponse: [function transformResponse(data) {\n    const transitional = this.transitional || defaults.transitional;\n    const forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    const JSONRequested = this.responseType === 'json';\n    if (utils.isResponse(data) || utils.isReadableStream(data)) {\n      return data;\n    }\n    if (data && utils.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {\n      const silentJSONParsing = transitional && transitional.silentJSONParsing;\n      const strictJSONParsing = !silentJSONParsing && JSONRequested;\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n          }\n          throw e;\n        }\n      }\n    }\n    return data;\n  }],\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n  maxContentLength: -1,\n  maxBodyLength: -1,\n  env: {\n    FormData: platform.classes.FormData,\n    Blob: platform.classes.Blob\n  },\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*',\n      'Content-Type': undefined\n    }\n  }\n};\nutils.forEach(['delete', 'get', 'head', 'post', 'put', 'patch'], method => {\n  defaults.headers[method] = {};\n});\nexport default defaults;", "map": {"version": 3, "names": ["utils", "AxiosError", "transitionalD<PERSON>ault<PERSON>", "toFormData", "toURLEncodedForm", "platform", "formDataToJSON", "stringifySafely", "rawValue", "parser", "encoder", "isString", "JSON", "parse", "trim", "e", "name", "stringify", "defaults", "transitional", "adapter", "transformRequest", "data", "headers", "contentType", "getContentType", "hasJSONContentType", "indexOf", "isObjectPayload", "isObject", "isHTMLForm", "FormData", "isFormData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isStream", "isFile", "isBlob", "isReadableStream", "isArrayBuffer<PERSON>iew", "buffer", "isURLSearchParams", "setContentType", "toString", "isFileList", "formSerializer", "_FormData", "env", "transformResponse", "forcedJSONParsing", "JSONRequested", "responseType", "isResponse", "silentJSONParsing", "strictJSONParsing", "from", "ERR_BAD_RESPONSE", "response", "timeout", "xsrfCookieName", "xsrfHeaderName", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "classes", "Blob", "validateStatus", "status", "common", "undefined", "for<PERSON>ach", "method"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/node_modules/axios/lib/defaults/index.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\nimport transitionalDefaults from './transitional.js';\nimport toFormData from '../helpers/toFormData.js';\nimport toURLEncodedForm from '../helpers/toURLEncodedForm.js';\nimport platform from '../platform/index.js';\nimport formDataToJSON from '../helpers/formDataToJSON.js';\n\n/**\n * It takes a string, tries to parse it, and if it fails, it returns the stringified version\n * of the input\n *\n * @param {any} rawValue - The value to be stringified.\n * @param {Function} parser - A function that parses a string into a JavaScript object.\n * @param {Function} encoder - A function that takes a value and returns a string.\n *\n * @returns {string} A stringified version of the rawValue.\n */\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n\n  return (encoder || JSON.stringify)(rawValue);\n}\n\nconst defaults = {\n\n  transitional: transitionalDefaults,\n\n  adapter: ['xhr', 'http', 'fetch'],\n\n  transformRequest: [function transformRequest(data, headers) {\n    const contentType = headers.getContentType() || '';\n    const hasJSONContentType = contentType.indexOf('application/json') > -1;\n    const isObjectPayload = utils.isObject(data);\n\n    if (isObjectPayload && utils.isHTMLForm(data)) {\n      data = new FormData(data);\n    }\n\n    const isFormData = utils.isFormData(data);\n\n    if (isFormData) {\n      return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;\n    }\n\n    if (utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data) ||\n      utils.isReadableStream(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      headers.setContentType('application/x-www-form-urlencoded;charset=utf-8', false);\n      return data.toString();\n    }\n\n    let isFileList;\n\n    if (isObjectPayload) {\n      if (contentType.indexOf('application/x-www-form-urlencoded') > -1) {\n        return toURLEncodedForm(data, this.formSerializer).toString();\n      }\n\n      if ((isFileList = utils.isFileList(data)) || contentType.indexOf('multipart/form-data') > -1) {\n        const _FormData = this.env && this.env.FormData;\n\n        return toFormData(\n          isFileList ? {'files[]': data} : data,\n          _FormData && new _FormData(),\n          this.formSerializer\n        );\n      }\n    }\n\n    if (isObjectPayload || hasJSONContentType ) {\n      headers.setContentType('application/json', false);\n      return stringifySafely(data);\n    }\n\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    const transitional = this.transitional || defaults.transitional;\n    const forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    const JSONRequested = this.responseType === 'json';\n\n    if (utils.isResponse(data) || utils.isReadableStream(data)) {\n      return data;\n    }\n\n    if (data && utils.isString(data) && ((forcedJSONParsing && !this.responseType) || JSONRequested)) {\n      const silentJSONParsing = transitional && transitional.silentJSONParsing;\n      const strictJSONParsing = !silentJSONParsing && JSONRequested;\n\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n          }\n          throw e;\n        }\n      }\n    }\n\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n  maxBodyLength: -1,\n\n  env: {\n    FormData: platform.classes.FormData,\n    Blob: platform.classes.Blob\n  },\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*',\n      'Content-Type': undefined\n    }\n  }\n};\n\nutils.forEach(['delete', 'get', 'head', 'post', 'put', 'patch'], (method) => {\n  defaults.headers[method] = {};\n});\n\nexport default defaults;\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,oBAAoB,MAAM,mBAAmB;AACpD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,cAAc,MAAM,8BAA8B;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAClD,IAAIV,KAAK,CAACW,QAAQ,CAACH,QAAQ,CAAC,EAAE;IAC5B,IAAI;MACF,CAACC,MAAM,IAAIG,IAAI,CAACC,KAAK,EAAEL,QAAQ,CAAC;MAChC,OAAOR,KAAK,CAACc,IAAI,CAACN,QAAQ,CAAC;IAC7B,CAAC,CAAC,OAAOO,CAAC,EAAE;MACV,IAAIA,CAAC,CAACC,IAAI,KAAK,aAAa,EAAE;QAC5B,MAAMD,CAAC;MACT;IACF;EACF;EAEA,OAAO,CAACL,OAAO,IAAIE,IAAI,CAACK,SAAS,EAAET,QAAQ,CAAC;AAC9C;AAEA,MAAMU,QAAQ,GAAG;EAEfC,YAAY,EAAEjB,oBAAoB;EAElCkB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;EAEjCC,gBAAgB,EAAE,CAAC,SAASA,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAC1D,MAAMC,WAAW,GAAGD,OAAO,CAACE,cAAc,CAAC,CAAC,IAAI,EAAE;IAClD,MAAMC,kBAAkB,GAAGF,WAAW,CAACG,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACvE,MAAMC,eAAe,GAAG5B,KAAK,CAAC6B,QAAQ,CAACP,IAAI,CAAC;IAE5C,IAAIM,eAAe,IAAI5B,KAAK,CAAC8B,UAAU,CAACR,IAAI,CAAC,EAAE;MAC7CA,IAAI,GAAG,IAAIS,QAAQ,CAACT,IAAI,CAAC;IAC3B;IAEA,MAAMU,UAAU,GAAGhC,KAAK,CAACgC,UAAU,CAACV,IAAI,CAAC;IAEzC,IAAIU,UAAU,EAAE;MACd,OAAON,kBAAkB,GAAGd,IAAI,CAACK,SAAS,CAACX,cAAc,CAACgB,IAAI,CAAC,CAAC,GAAGA,IAAI;IACzE;IAEA,IAAItB,KAAK,CAACiC,aAAa,CAACX,IAAI,CAAC,IAC3BtB,KAAK,CAACkC,QAAQ,CAACZ,IAAI,CAAC,IACpBtB,KAAK,CAACmC,QAAQ,CAACb,IAAI,CAAC,IACpBtB,KAAK,CAACoC,MAAM,CAACd,IAAI,CAAC,IAClBtB,KAAK,CAACqC,MAAM,CAACf,IAAI,CAAC,IAClBtB,KAAK,CAACsC,gBAAgB,CAAChB,IAAI,CAAC,EAC5B;MACA,OAAOA,IAAI;IACb;IACA,IAAItB,KAAK,CAACuC,iBAAiB,CAACjB,IAAI,CAAC,EAAE;MACjC,OAAOA,IAAI,CAACkB,MAAM;IACpB;IACA,IAAIxC,KAAK,CAACyC,iBAAiB,CAACnB,IAAI,CAAC,EAAE;MACjCC,OAAO,CAACmB,cAAc,CAAC,iDAAiD,EAAE,KAAK,CAAC;MAChF,OAAOpB,IAAI,CAACqB,QAAQ,CAAC,CAAC;IACxB;IAEA,IAAIC,UAAU;IAEd,IAAIhB,eAAe,EAAE;MACnB,IAAIJ,WAAW,CAACG,OAAO,CAAC,mCAAmC,CAAC,GAAG,CAAC,CAAC,EAAE;QACjE,OAAOvB,gBAAgB,CAACkB,IAAI,EAAE,IAAI,CAACuB,cAAc,CAAC,CAACF,QAAQ,CAAC,CAAC;MAC/D;MAEA,IAAI,CAACC,UAAU,GAAG5C,KAAK,CAAC4C,UAAU,CAACtB,IAAI,CAAC,KAAKE,WAAW,CAACG,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,EAAE;QAC5F,MAAMmB,SAAS,GAAG,IAAI,CAACC,GAAG,IAAI,IAAI,CAACA,GAAG,CAAChB,QAAQ;QAE/C,OAAO5B,UAAU,CACfyC,UAAU,GAAG;UAAC,SAAS,EAAEtB;QAAI,CAAC,GAAGA,IAAI,EACrCwB,SAAS,IAAI,IAAIA,SAAS,CAAC,CAAC,EAC5B,IAAI,CAACD,cACP,CAAC;MACH;IACF;IAEA,IAAIjB,eAAe,IAAIF,kBAAkB,EAAG;MAC1CH,OAAO,CAACmB,cAAc,CAAC,kBAAkB,EAAE,KAAK,CAAC;MACjD,OAAOnC,eAAe,CAACe,IAAI,CAAC;IAC9B;IAEA,OAAOA,IAAI;EACb,CAAC,CAAC;EAEF0B,iBAAiB,EAAE,CAAC,SAASA,iBAAiBA,CAAC1B,IAAI,EAAE;IACnD,MAAMH,YAAY,GAAG,IAAI,CAACA,YAAY,IAAID,QAAQ,CAACC,YAAY;IAC/D,MAAM8B,iBAAiB,GAAG9B,YAAY,IAAIA,YAAY,CAAC8B,iBAAiB;IACxE,MAAMC,aAAa,GAAG,IAAI,CAACC,YAAY,KAAK,MAAM;IAElD,IAAInD,KAAK,CAACoD,UAAU,CAAC9B,IAAI,CAAC,IAAItB,KAAK,CAACsC,gBAAgB,CAAChB,IAAI,CAAC,EAAE;MAC1D,OAAOA,IAAI;IACb;IAEA,IAAIA,IAAI,IAAItB,KAAK,CAACW,QAAQ,CAACW,IAAI,CAAC,KAAM2B,iBAAiB,IAAI,CAAC,IAAI,CAACE,YAAY,IAAKD,aAAa,CAAC,EAAE;MAChG,MAAMG,iBAAiB,GAAGlC,YAAY,IAAIA,YAAY,CAACkC,iBAAiB;MACxE,MAAMC,iBAAiB,GAAG,CAACD,iBAAiB,IAAIH,aAAa;MAE7D,IAAI;QACF,OAAOtC,IAAI,CAACC,KAAK,CAACS,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOP,CAAC,EAAE;QACV,IAAIuC,iBAAiB,EAAE;UACrB,IAAIvC,CAAC,CAACC,IAAI,KAAK,aAAa,EAAE;YAC5B,MAAMf,UAAU,CAACsD,IAAI,CAACxC,CAAC,EAAEd,UAAU,CAACuD,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAACC,QAAQ,CAAC;UAClF;UACA,MAAM1C,CAAC;QACT;MACF;IACF;IAEA,OAAOO,IAAI;EACb,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEoC,OAAO,EAAE,CAAC;EAEVC,cAAc,EAAE,YAAY;EAC5BC,cAAc,EAAE,cAAc;EAE9BC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,aAAa,EAAE,CAAC,CAAC;EAEjBf,GAAG,EAAE;IACHhB,QAAQ,EAAE1B,QAAQ,CAAC0D,OAAO,CAAChC,QAAQ;IACnCiC,IAAI,EAAE3D,QAAQ,CAAC0D,OAAO,CAACC;EACzB,CAAC;EAEDC,cAAc,EAAE,SAASA,cAAcA,CAACC,MAAM,EAAE;IAC9C,OAAOA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;EACtC,CAAC;EAED3C,OAAO,EAAE;IACP4C,MAAM,EAAE;MACN,QAAQ,EAAE,mCAAmC;MAC7C,cAAc,EAAEC;IAClB;EACF;AACF,CAAC;AAEDpE,KAAK,CAACqE,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAGC,MAAM,IAAK;EAC3EpD,QAAQ,CAACK,OAAO,CAAC+C,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEF,eAAepD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}