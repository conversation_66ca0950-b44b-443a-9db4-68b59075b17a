.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
}

.auth-container {
  display: flex;
  max-width: 1000px;
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.auth-form {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.register-form {
  max-height: 80vh;
  overflow-y: auto;
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h2 {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.auth-header p {
  color: #7f8c8d;
  font-size: 1rem;
}

.auth-form form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 600;
  color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #e74c3c;
}

.form-group input.error {
  border-color: #e74c3c;
}

.form-group small {
  margin-top: 5px;
  font-size: 14px;
  color: #7f8c8d;
}

.form-group small.error {
  color: #e74c3c;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #7f8c8d;
}

.address-section {
  border-top: 1px solid #eee;
  padding-top: 20px;
  margin-top: 10px;
}

.address-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.auth-btn {
  padding: 14px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 10px;
}

.auth-btn:hover:not(:disabled) {
  background: #c0392b;
}

.auth-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
}

.auth-footer {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.auth-footer p {
  color: #7f8c8d;
}

.auth-footer a {
  color: #e74c3c;
  text-decoration: none;
  font-weight: 600;
  margin-left: 5px;
}

.auth-footer a:hover {
  text-decoration: underline;
}

.demo-accounts {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.demo-accounts h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 14px;
}

.demo-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.demo-btn {
  padding: 8px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.demo-btn:hover {
  background: #2980b9;
}

.auth-image {
  flex: 1;
  position: relative;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
}

.auth-image-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 1;
}

.auth-image-overlay h3 {
  font-size: 2rem;
  margin-bottom: 15px;
}

.auth-image-overlay p {
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 300px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    flex-direction: column;
    max-width: 500px;
  }

  .auth-form {
    padding: 30px 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .auth-image {
    min-height: 200px;
  }

  .auth-image-overlay h3 {
    font-size: 1.5rem;
  }

  .auth-image-overlay p {
    font-size: 1rem;
  }

  .demo-buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .auth-page {
    padding: 10px;
  }

  .auth-form {
    padding: 20px 15px;
  }

  .auth-header h2 {
    font-size: 1.5rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
