{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Restaurant App\\\\frontend\\\\src\\\\pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport '../styles/Auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  var _location$state, _location$state$from, _location$state2;\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    address: {\n      street: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      country: 'USA'\n    }\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [passwordMatch, setPasswordMatch] = useState(true);\n  const {\n    register,\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, navigate, from]);\n  useEffect(() => {\n    if (formData.password && formData.confirmPassword) {\n      setPasswordMatch(formData.password === formData.confirmPassword);\n    }\n  }, [formData.password, formData.confirmPassword]);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name.startsWith('address.')) {\n      const addressField = name.split('.')[1];\n      setFormData({\n        ...formData,\n        address: {\n          ...formData.address,\n          [addressField]: value\n        }\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!passwordMatch) {\n      return;\n    }\n    if (formData.password.length < 6) {\n      return;\n    }\n    const {\n      confirmPassword,\n      ...registrationData\n    } = formData;\n    const result = await register(registrationData);\n    if (result.success) {\n      navigate(from, {\n        replace: true\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-form register-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Join us and start your culinary journey\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleChange,\n                required: true,\n                placeholder: \"Enter your full name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                children: \"Email Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"email\",\n                name: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                required: true,\n                placeholder: \"Enter your email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"password-input\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPassword ? 'text' : 'password',\n                  id: \"password\",\n                  name: \"password\",\n                  value: formData.password,\n                  onChange: handleChange,\n                  required: true,\n                  placeholder: \"Enter your password\",\n                  minLength: \"6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"password-toggle\",\n                  onClick: () => setShowPassword(!showPassword),\n                  children: showPassword ? '👁️' : '👁️‍🗨️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"Password must be at least 6 characters long\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"confirmPassword\",\n                children: \"Confirm Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"password-input\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showConfirmPassword ? 'text' : 'password',\n                  id: \"confirmPassword\",\n                  name: \"confirmPassword\",\n                  value: formData.confirmPassword,\n                  onChange: handleChange,\n                  required: true,\n                  placeholder: \"Confirm your password\",\n                  className: !passwordMatch ? 'error' : ''\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"password-toggle\",\n                  onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                  children: showConfirmPassword ? '👁️' : '👁️‍🗨️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), !passwordMatch && /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"error\",\n                children: \"Passwords do not match\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 36\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"phone\",\n              children: \"Phone Number (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              id: \"phone\",\n              name: \"phone\",\n              value: formData.phone,\n              onChange: handleChange,\n              placeholder: \"Enter your phone number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"address-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Address (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"address.street\",\n                  value: formData.address.street,\n                  onChange: handleChange,\n                  placeholder: \"Street Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"address.city\",\n                  value: formData.address.city,\n                  onChange: handleChange,\n                  placeholder: \"City\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"address.state\",\n                  value: formData.address.state,\n                  onChange: handleChange,\n                  placeholder: \"State\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"address.zipCode\",\n                  value: formData.address.zipCode,\n                  onChange: handleChange,\n                  placeholder: \"ZIP Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"auth-btn\",\n            disabled: loading || !passwordMatch,\n            children: loading ? 'Creating Account...' : 'Create Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Already have an account?\", /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              state: {\n                from: (_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.from\n              },\n              children: \"Sign in here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-image\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/api/placeholder/500/600\",\n          alt: \"Restaurant chef\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-image-overlay\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Join Our Community\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Become part of our family and enjoy exclusive benefits\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"TaFx0U5JqwANyVZ/4JAo1rKL5Jg=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Register", "_s", "_location$state", "_location$state$from", "_location$state2", "formData", "setFormData", "name", "email", "password", "confirmPassword", "phone", "address", "street", "city", "state", "zipCode", "country", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "passwordMatch", "setPasswordMatch", "register", "isAuthenticated", "loading", "navigate", "location", "from", "pathname", "replace", "handleChange", "e", "value", "target", "startsWith", "addressField", "split", "handleSubmit", "preventDefault", "length", "registrationData", "result", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "disabled", "to", "src", "alt", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/src/pages/Register.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport '../styles/Auth.css';\n\nconst Register = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    address: {\n      street: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      country: 'USA'\n    }\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [passwordMatch, setPasswordMatch] = useState(true);\n\n  const { register, isAuthenticated, loading } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const from = location.state?.from?.pathname || '/';\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate(from, { replace: true });\n    }\n  }, [isAuthenticated, navigate, from]);\n\n  useEffect(() => {\n    if (formData.password && formData.confirmPassword) {\n      setPasswordMatch(formData.password === formData.confirmPassword);\n    }\n  }, [formData.password, formData.confirmPassword]);\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    \n    if (name.startsWith('address.')) {\n      const addressField = name.split('.')[1];\n      setFormData({\n        ...formData,\n        address: {\n          ...formData.address,\n          [addressField]: value\n        }\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!passwordMatch) {\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      return;\n    }\n\n    const { confirmPassword, ...registrationData } = formData;\n    \n    const result = await register(registrationData);\n    if (result.success) {\n      navigate(from, { replace: true });\n    }\n  };\n\n  return (\n    <div className=\"auth-page\">\n      <div className=\"auth-container\">\n        <div className=\"auth-form register-form\">\n          <div className=\"auth-header\">\n            <h2>Create Account</h2>\n            <p>Join us and start your culinary journey</p>\n          </div>\n\n          <form onSubmit={handleSubmit}>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"name\">Full Name</label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  required\n                  placeholder=\"Enter your full name\"\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"email\">Email Address</label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  required\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label htmlFor=\"password\">Password</label>\n                <div className=\"password-input\">\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    id=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleChange}\n                    required\n                    placeholder=\"Enter your password\"\n                    minLength=\"6\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"password-toggle\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? '👁️' : '👁️‍🗨️'}\n                  </button>\n                </div>\n                <small>Password must be at least 6 characters long</small>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"confirmPassword\">Confirm Password</label>\n                <div className=\"password-input\">\n                  <input\n                    type={showConfirmPassword ? 'text' : 'password'}\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    value={formData.confirmPassword}\n                    onChange={handleChange}\n                    required\n                    placeholder=\"Confirm your password\"\n                    className={!passwordMatch ? 'error' : ''}\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"password-toggle\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  >\n                    {showConfirmPassword ? '👁️' : '👁️‍🗨️'}\n                  </button>\n                </div>\n                {!passwordMatch && <small className=\"error\">Passwords do not match</small>}\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"phone\">Phone Number (Optional)</label>\n              <input\n                type=\"tel\"\n                id=\"phone\"\n                name=\"phone\"\n                value={formData.phone}\n                onChange={handleChange}\n                placeholder=\"Enter your phone number\"\n              />\n            </div>\n\n            <div className=\"address-section\">\n              <h4>Address (Optional)</h4>\n              <div className=\"form-row\">\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    name=\"address.street\"\n                    value={formData.address.street}\n                    onChange={handleChange}\n                    placeholder=\"Street Address\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    name=\"address.city\"\n                    value={formData.address.city}\n                    onChange={handleChange}\n                    placeholder=\"City\"\n                  />\n                </div>\n              </div>\n              <div className=\"form-row\">\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    name=\"address.state\"\n                    value={formData.address.state}\n                    onChange={handleChange}\n                    placeholder=\"State\"\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    name=\"address.zipCode\"\n                    value={formData.address.zipCode}\n                    onChange={handleChange}\n                    placeholder=\"ZIP Code\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            <button \n              type=\"submit\" \n              className=\"auth-btn\"\n              disabled={loading || !passwordMatch}\n            >\n              {loading ? 'Creating Account...' : 'Create Account'}\n            </button>\n          </form>\n\n          <div className=\"auth-footer\">\n            <p>\n              Already have an account? \n              <Link to=\"/login\" state={{ from: location.state?.from }}>\n                Sign in here\n              </Link>\n            </p>\n          </div>\n        </div>\n\n        <div className=\"auth-image\">\n          <img src=\"/api/placeholder/500/600\" alt=\"Restaurant chef\" />\n          <div className=\"auth-image-overlay\">\n            <h3>Join Our Community</h3>\n            <p>Become part of our family and enjoy exclusive benefits</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC;IACvCe,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAM;IAAEgC,QAAQ;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAG7B,OAAO,CAAC,CAAC;EACxD,MAAM8B,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAMiC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAE9B,MAAMiC,IAAI,GAAG,EAAA3B,eAAA,GAAA0B,QAAQ,CAACb,KAAK,cAAAb,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB2B,IAAI,cAAA1B,oBAAA,uBAApBA,oBAAA,CAAsB2B,QAAQ,KAAI,GAAG;EAElDrC,SAAS,CAAC,MAAM;IACd,IAAIgC,eAAe,EAAE;MACnBE,QAAQ,CAACE,IAAI,EAAE;QAAEE,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACN,eAAe,EAAEE,QAAQ,EAAEE,IAAI,CAAC,CAAC;EAErCpC,SAAS,CAAC,MAAM;IACd,IAAIY,QAAQ,CAACI,QAAQ,IAAIJ,QAAQ,CAACK,eAAe,EAAE;MACjDa,gBAAgB,CAAClB,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,CAAC;IAClE;EACF,CAAC,EAAE,CAACL,QAAQ,CAACI,QAAQ,EAAEJ,QAAQ,CAACK,eAAe,CAAC,CAAC;EAEjD,MAAMsB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE1B,IAAI;MAAE2B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAEhC,IAAI5B,IAAI,CAAC6B,UAAU,CAAC,UAAU,CAAC,EAAE;MAC/B,MAAMC,YAAY,GAAG9B,IAAI,CAAC+B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACvChC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXO,OAAO,EAAE;UACP,GAAGP,QAAQ,CAACO,OAAO;UACnB,CAACyB,YAAY,GAAGH;QAClB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL5B,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAG2B;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMK,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAElB,IAAI,CAAClB,aAAa,EAAE;MAClB;IACF;IAEA,IAAIjB,QAAQ,CAACI,QAAQ,CAACgC,MAAM,GAAG,CAAC,EAAE;MAChC;IACF;IAEA,MAAM;MAAE/B,eAAe;MAAE,GAAGgC;IAAiB,CAAC,GAAGrC,QAAQ;IAEzD,MAAMsC,MAAM,GAAG,MAAMnB,QAAQ,CAACkB,gBAAgB,CAAC;IAC/C,IAAIC,MAAM,CAACC,OAAO,EAAE;MAClBjB,QAAQ,CAACE,IAAI,EAAE;QAAEE,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC;EAED,oBACEhC,OAAA;IAAK8C,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxB/C,OAAA;MAAK8C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/C,OAAA;QAAK8C,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC/C,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/C,OAAA;YAAA+C,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBnD,OAAA;YAAA+C,QAAA,EAAG;UAAuC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAENnD,OAAA;UAAMoD,QAAQ,EAAEZ,YAAa;UAAAO,QAAA,gBAC3B/C,OAAA;YAAK8C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB/C,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/C,OAAA;gBAAOqD,OAAO,EAAC,MAAM;gBAAAN,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvCnD,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,MAAM;gBACT/C,IAAI,EAAC,MAAM;gBACX2B,KAAK,EAAE7B,QAAQ,CAACE,IAAK;gBACrBgD,QAAQ,EAAEvB,YAAa;gBACvBwB,QAAQ;gBACRC,WAAW,EAAC;cAAsB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnD,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/C,OAAA;gBAAOqD,OAAO,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5CnD,OAAA;gBACEsD,IAAI,EAAC,OAAO;gBACZC,EAAE,EAAC,OAAO;gBACV/C,IAAI,EAAC,OAAO;gBACZ2B,KAAK,EAAE7B,QAAQ,CAACG,KAAM;gBACtB+C,QAAQ,EAAEvB,YAAa;gBACvBwB,QAAQ;gBACRC,WAAW,EAAC;cAAkB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnD,OAAA;YAAK8C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB/C,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/C,OAAA;gBAAOqD,OAAO,EAAC,UAAU;gBAAAN,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CnD,OAAA;gBAAK8C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B/C,OAAA;kBACEsD,IAAI,EAAEnC,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCoC,EAAE,EAAC,UAAU;kBACb/C,IAAI,EAAC,UAAU;kBACf2B,KAAK,EAAE7B,QAAQ,CAACI,QAAS;kBACzB8C,QAAQ,EAAEvB,YAAa;kBACvBwB,QAAQ;kBACRC,WAAW,EAAC,qBAAqB;kBACjCC,SAAS,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACFnD,OAAA;kBACEsD,IAAI,EAAC,QAAQ;kBACbR,SAAS,EAAC,iBAAiB;kBAC3Bc,OAAO,EAAEA,CAAA,KAAMxC,eAAe,CAAC,CAACD,YAAY,CAAE;kBAAA4B,QAAA,EAE7C5B,YAAY,GAAG,KAAK,GAAG;gBAAS;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNnD,OAAA;gBAAA+C,QAAA,EAAO;cAA2C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAENnD,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB/C,OAAA;gBAAOqD,OAAO,EAAC,iBAAiB;gBAAAN,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDnD,OAAA;gBAAK8C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B/C,OAAA;kBACEsD,IAAI,EAAEjC,mBAAmB,GAAG,MAAM,GAAG,UAAW;kBAChDkC,EAAE,EAAC,iBAAiB;kBACpB/C,IAAI,EAAC,iBAAiB;kBACtB2B,KAAK,EAAE7B,QAAQ,CAACK,eAAgB;kBAChC6C,QAAQ,EAAEvB,YAAa;kBACvBwB,QAAQ;kBACRC,WAAW,EAAC,uBAAuB;kBACnCZ,SAAS,EAAE,CAACvB,aAAa,GAAG,OAAO,GAAG;gBAAG;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACFnD,OAAA;kBACEsD,IAAI,EAAC,QAAQ;kBACbR,SAAS,EAAC,iBAAiB;kBAC3Bc,OAAO,EAAEA,CAAA,KAAMtC,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;kBAAA0B,QAAA,EAE3D1B,mBAAmB,GAAG,KAAK,GAAG;gBAAS;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACL,CAAC5B,aAAa,iBAAIvB,OAAA;gBAAO8C,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnD,OAAA;YAAK8C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB/C,OAAA;cAAOqD,OAAO,EAAC,OAAO;cAAAN,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDnD,OAAA;cACEsD,IAAI,EAAC,KAAK;cACVC,EAAE,EAAC,OAAO;cACV/C,IAAI,EAAC,OAAO;cACZ2B,KAAK,EAAE7B,QAAQ,CAACM,KAAM;cACtB4C,QAAQ,EAAEvB,YAAa;cACvByB,WAAW,EAAC;YAAyB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnD,OAAA;YAAK8C,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B/C,OAAA;cAAA+C,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BnD,OAAA;cAAK8C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/C,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzB/C,OAAA;kBACEsD,IAAI,EAAC,MAAM;kBACX9C,IAAI,EAAC,gBAAgB;kBACrB2B,KAAK,EAAE7B,QAAQ,CAACO,OAAO,CAACC,MAAO;kBAC/B0C,QAAQ,EAAEvB,YAAa;kBACvByB,WAAW,EAAC;gBAAgB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzB/C,OAAA;kBACEsD,IAAI,EAAC,MAAM;kBACX9C,IAAI,EAAC,cAAc;kBACnB2B,KAAK,EAAE7B,QAAQ,CAACO,OAAO,CAACE,IAAK;kBAC7ByC,QAAQ,EAAEvB,YAAa;kBACvByB,WAAW,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/C,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzB/C,OAAA;kBACEsD,IAAI,EAAC,MAAM;kBACX9C,IAAI,EAAC,eAAe;kBACpB2B,KAAK,EAAE7B,QAAQ,CAACO,OAAO,CAACG,KAAM;kBAC9BwC,QAAQ,EAAEvB,YAAa;kBACvByB,WAAW,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnD,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzB/C,OAAA;kBACEsD,IAAI,EAAC,MAAM;kBACX9C,IAAI,EAAC,iBAAiB;kBACtB2B,KAAK,EAAE7B,QAAQ,CAACO,OAAO,CAACI,OAAQ;kBAChCuC,QAAQ,EAAEvB,YAAa;kBACvByB,WAAW,EAAC;gBAAU;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnD,OAAA;YACEsD,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,UAAU;YACpBe,QAAQ,EAAElC,OAAO,IAAI,CAACJ,aAAc;YAAAwB,QAAA,EAEnCpB,OAAO,GAAG,qBAAqB,GAAG;UAAgB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEPnD,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B/C,OAAA;YAAA+C,QAAA,GAAG,0BAED,eAAA/C,OAAA,CAACL,IAAI;cAACmE,EAAE,EAAC,QAAQ;cAAC9C,KAAK,EAAE;gBAAEc,IAAI,GAAAzB,gBAAA,GAAEwB,QAAQ,CAACb,KAAK,cAAAX,gBAAA,uBAAdA,gBAAA,CAAgByB;cAAK,CAAE;cAAAiB,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnD,OAAA;QAAK8C,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/C,OAAA;UAAK+D,GAAG,EAAC,0BAA0B;UAACC,GAAG,EAAC;QAAiB;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DnD,OAAA;UAAK8C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC/C,OAAA;YAAA+C,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BnD,OAAA;YAAA+C,QAAA,EAAG;UAAsD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CAzPID,QAAQ;EAAA,QAmBmCH,OAAO,EACrCF,WAAW,EACXC,WAAW;AAAA;AAAAoE,EAAA,GArBxBhE,QAAQ;AA2Pd,eAAeA,QAAQ;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}