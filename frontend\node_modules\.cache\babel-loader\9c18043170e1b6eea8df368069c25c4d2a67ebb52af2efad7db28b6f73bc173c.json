{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Restaurant App\\\\frontend\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { useCart } from '../context/CartContext';\nimport Cart from './Cart';\nimport '../styles/Navbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const {\n    totalItems\n  } = useCart();\n  const navigate = useNavigate();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isCartOpen, setIsCartOpen] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n    setIsMenuOpen(false);\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  const toggleCart = () => {\n    setIsCartOpen(!isCartOpen);\n  };\n  const closeMenu = () => {\n    setIsMenuOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"navbar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-container\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"nav-logo\",\n          onClick: closeMenu,\n          children: \"\\uD83C\\uDF7D\\uFE0F Delicious Restaurant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `nav-menu ${isMenuOpen ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"nav-link\",\n            onClick: closeMenu,\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/menu\",\n            className: \"nav-link\",\n            onClick: closeMenu,\n            children: \"Menu\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/reservations\",\n            className: \"nav-link\",\n            onClick: closeMenu,\n            children: \"Reservations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"nav-link\",\n            onClick: closeMenu,\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/orders\",\n              className: \"nav-link\",\n              onClick: closeMenu,\n              children: \"My Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/profile\",\n              className: \"nav-link\",\n              onClick: closeMenu,\n              children: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin/dashboard\",\n              className: \"nav-link admin-link\",\n              onClick: closeMenu,\n              children: \"Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"nav-link logout-btn\",\n              onClick: handleLogout,\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"nav-link\",\n              onClick: closeMenu,\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"nav-link\",\n              onClick: closeMenu,\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"cart-btn\",\n            onClick: toggleCart,\n            children: [\"\\uD83D\\uDED2 Cart \", totalItems > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"cart-count\",\n              children: totalItems\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 42\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-toggle\",\n          onClick: toggleMenu,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Cart, {\n      isOpen: isCartOpen,\n      onClose: () => setIsCartOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navbar, \"l8j36PAXOkWT6WRcEhM350CbSiI=\", false, function () {\n  return [useAuth, useCart, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "useCart", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "user", "isAuthenticated", "logout", "totalItems", "navigate", "isMenuOpen", "setIsMenuOpen", "isCartOpen", "setIsCartOpen", "handleLogout", "toggleMenu", "toggleCart", "closeMenu", "children", "className", "to", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/src/components/Navbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { useCart } from '../context/CartContext';\nimport Cart from './Cart';\nimport '../styles/Navbar.css';\n\nconst Navbar = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const { totalItems } = useCart();\n  const navigate = useNavigate();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isCartOpen, setIsCartOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n    setIsMenuOpen(false);\n  };\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const toggleCart = () => {\n    setIsCartOpen(!isCartOpen);\n  };\n\n  const closeMenu = () => {\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <>\n      <nav className=\"navbar\">\n        <div className=\"nav-container\">\n          <Link to=\"/\" className=\"nav-logo\" onClick={closeMenu}>\n            🍽️ Delicious Restaurant\n          </Link>\n          \n          <div className={`nav-menu ${isMenuOpen ? 'active' : ''}`}>\n            <Link to=\"/\" className=\"nav-link\" onClick={closeMenu}>\n              Home\n            </Link>\n            <Link to=\"/menu\" className=\"nav-link\" onClick={closeMenu}>\n              Menu\n            </Link>\n            <Link to=\"/reservations\" className=\"nav-link\" onClick={closeMenu}>\n              Reservations\n            </Link>\n            <Link to=\"/contact\" className=\"nav-link\" onClick={closeMenu}>\n              Contact\n            </Link>\n            \n            {isAuthenticated ? (\n              <>\n                <Link to=\"/orders\" className=\"nav-link\" onClick={closeMenu}>\n                  My Orders\n                </Link>\n                <Link to=\"/profile\" className=\"nav-link\" onClick={closeMenu}>\n                  Profile\n                </Link>\n                {user?.role === 'admin' && (\n                  <Link to=\"/admin/dashboard\" className=\"nav-link admin-link\" onClick={closeMenu}>\n                    Admin\n                  </Link>\n                )}\n                <button className=\"nav-link logout-btn\" onClick={handleLogout}>\n                  Logout\n                </button>\n              </>\n            ) : (\n              <>\n                <Link to=\"/login\" className=\"nav-link\" onClick={closeMenu}>\n                  Login\n                </Link>\n                <Link to=\"/register\" className=\"nav-link\" onClick={closeMenu}>\n                  Register\n                </Link>\n              </>\n            )}\n            \n            <button className=\"cart-btn\" onClick={toggleCart}>\n              🛒 Cart {totalItems > 0 && <span className=\"cart-count\">{totalItems}</span>}\n            </button>\n          </div>\n          \n          <div className=\"nav-toggle\" onClick={toggleMenu}>\n            <span className=\"bar\"></span>\n            <span className=\"bar\"></span>\n            <span className=\"bar\"></span>\n          </div>\n        </div>\n      </nav>\n      \n      {/* Cart Sidebar */}\n      <Cart isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />\n    </>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGX,OAAO,CAAC,CAAC;EACnD,MAAM;IAAEY;EAAW,CAAC,GAAGX,OAAO,CAAC,CAAC;EAChC,MAAMY,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMqB,YAAY,GAAGA,CAAA,KAAM;IACzBP,MAAM,CAAC,CAAC;IACRE,QAAQ,CAAC,GAAG,CAAC;IACbE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvBJ,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvBH,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtBN,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,oBACEX,OAAA,CAAAE,SAAA;IAAAgB,QAAA,gBACElB,OAAA;MAAKmB,SAAS,EAAC,QAAQ;MAAAD,QAAA,eACrBlB,OAAA;QAAKmB,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC5BlB,OAAA,CAACN,IAAI;UAAC0B,EAAE,EAAC,GAAG;UAACD,SAAS,EAAC,UAAU;UAACE,OAAO,EAAEJ,SAAU;UAAAC,QAAA,EAAC;QAEtD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEPzB,OAAA;UAAKmB,SAAS,EAAE,YAAYT,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAQ,QAAA,gBACvDlB,OAAA,CAACN,IAAI;YAAC0B,EAAE,EAAC,GAAG;YAACD,SAAS,EAAC,UAAU;YAACE,OAAO,EAAEJ,SAAU;YAAAC,QAAA,EAAC;UAEtD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA,CAACN,IAAI;YAAC0B,EAAE,EAAC,OAAO;YAACD,SAAS,EAAC,UAAU;YAACE,OAAO,EAAEJ,SAAU;YAAAC,QAAA,EAAC;UAE1D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA,CAACN,IAAI;YAAC0B,EAAE,EAAC,eAAe;YAACD,SAAS,EAAC,UAAU;YAACE,OAAO,EAAEJ,SAAU;YAAAC,QAAA,EAAC;UAElE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA,CAACN,IAAI;YAAC0B,EAAE,EAAC,UAAU;YAACD,SAAS,EAAC,UAAU;YAACE,OAAO,EAAEJ,SAAU;YAAAC,QAAA,EAAC;UAE7D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAENnB,eAAe,gBACdN,OAAA,CAAAE,SAAA;YAAAgB,QAAA,gBACElB,OAAA,CAACN,IAAI;cAAC0B,EAAE,EAAC,SAAS;cAACD,SAAS,EAAC,UAAU;cAACE,OAAO,EAAEJ,SAAU;cAAAC,QAAA,EAAC;YAE5D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzB,OAAA,CAACN,IAAI;cAAC0B,EAAE,EAAC,UAAU;cAACD,SAAS,EAAC,UAAU;cAACE,OAAO,EAAEJ,SAAU;cAAAC,QAAA,EAAC;YAE7D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACN,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,MAAK,OAAO,iBACrB1B,OAAA,CAACN,IAAI;cAAC0B,EAAE,EAAC,kBAAkB;cAACD,SAAS,EAAC,qBAAqB;cAACE,OAAO,EAAEJ,SAAU;cAAAC,QAAA,EAAC;YAEhF;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP,eACDzB,OAAA;cAAQmB,SAAS,EAAC,qBAAqB;cAACE,OAAO,EAAEP,YAAa;cAAAI,QAAA,EAAC;YAE/D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHzB,OAAA,CAAAE,SAAA;YAAAgB,QAAA,gBACElB,OAAA,CAACN,IAAI;cAAC0B,EAAE,EAAC,QAAQ;cAACD,SAAS,EAAC,UAAU;cAACE,OAAO,EAAEJ,SAAU;cAAAC,QAAA,EAAC;YAE3D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzB,OAAA,CAACN,IAAI;cAAC0B,EAAE,EAAC,WAAW;cAACD,SAAS,EAAC,UAAU;cAACE,OAAO,EAAEJ,SAAU;cAAAC,QAAA,EAAC;YAE9D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH,eAEDzB,OAAA;YAAQmB,SAAS,EAAC,UAAU;YAACE,OAAO,EAAEL,UAAW;YAAAE,QAAA,GAAC,oBACxC,EAACV,UAAU,GAAG,CAAC,iBAAIR,OAAA;cAAMmB,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAEV;YAAU;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzB,OAAA;UAAKmB,SAAS,EAAC,YAAY;UAACE,OAAO,EAAEN,UAAW;UAAAG,QAAA,gBAC9ClB,OAAA;YAAMmB,SAAS,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7BzB,OAAA;YAAMmB,SAAS,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7BzB,OAAA;YAAMmB,SAAS,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA,CAACF,IAAI;MAAC6B,MAAM,EAAEf,UAAW;MAACgB,OAAO,EAAEA,CAAA,KAAMf,aAAa,CAAC,KAAK;IAAE;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACjE,CAAC;AAEP,CAAC;AAACrB,EAAA,CA5FID,MAAM;EAAA,QACgCP,OAAO,EAC1BC,OAAO,EACbF,WAAW;AAAA;AAAAkC,EAAA,GAHxB1B,MAAM;AA8FZ,eAAeA,MAAM;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}