const Order = require('../models/Order');
const Dish = require('../models/Dish');
const { validationResult } = require('express-validator');

// @desc    Create new order
// @route   POST /api/orders
// @access  Private
const createOrder = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { items, orderType, deliveryAddress, paymentMethod, notes } = req.body;

    // Validate items and calculate total
    let totalAmount = 0;
    const orderItems = [];

    for (const item of items) {
      const dish = await Dish.findById(item.dish);
      if (!dish) {
        return res.status(400).json({ message: `Dish with ID ${item.dish} not found` });
      }
      if (!dish.isAvailable) {
        return res.status(400).json({ message: `${dish.name} is currently unavailable` });
      }

      const orderItem = {
        dish: dish._id,
        quantity: item.quantity,
        price: dish.price,
        specialInstructions: item.specialInstructions || ''
      };

      orderItems.push(orderItem);
      totalAmount += dish.price * item.quantity;
    }

    // Create order
    const order = await Order.create({
      customer: req.user._id,
      items: orderItems,
      totalAmount,
      orderType,
      deliveryAddress: orderType === 'delivery' ? deliveryAddress : undefined,
      paymentMethod,
      notes,
      estimatedDeliveryTime: new Date(Date.now() + 45 * 60 * 1000) // 45 minutes from now
    });

    // Populate the order with dish details
    const populatedOrder = await Order.findById(order._id)
      .populate('customer', 'name email phone')
      .populate('items.dish', 'name price image');

    // Emit real-time update to admin
    const io = req.app.get('io');
    io.to('admin').emit('newOrder', populatedOrder);

    res.status(201).json(populatedOrder);
  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get user orders
// @route   GET /api/orders
// @access  Private
const getUserOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const orders = await Order.find({ customer: req.user._id })
      .populate('items.dish', 'name price image')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    const total = await Order.countDocuments({ customer: req.user._id });

    res.json({
      orders,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Get user orders error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get single order
// @route   GET /api/orders/:id
// @access  Private
const getOrder = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('customer', 'name email phone')
      .populate('items.dish', 'name price image description');

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    // Check if user owns the order or is admin
    if (order.customer._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(order);
  } catch (error) {
    console.error('Get order error:', error);
    if (error.name === 'CastError') {
      return res.status(404).json({ message: 'Order not found' });
    }
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get all orders (Admin only)
// @route   GET /api/orders/admin/all
// @access  Private/Admin
const getAllOrders = async (req, res) => {
  try {
    const { 
      status, 
      orderType, 
      page = 1, 
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    let query = {};
    if (status && status !== 'all') {
      query.status = status;
    }
    if (orderType && orderType !== 'all') {
      query.orderType = orderType;
    }

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const orders = await Order.find(query)
      .populate('customer', 'name email phone')
      .populate('items.dish', 'name price')
      .sort(sortOptions)
      .skip(skip)
      .limit(limitNum);

    const total = await Order.countDocuments(query);

    res.json({
      orders,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Get all orders error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update order status (Admin only)
// @route   PUT /api/orders/:id/status
// @access  Private/Admin
const updateOrderStatus = async (req, res) => {
  try {
    const { status } = req.body;
    
    const order = await Order.findById(req.params.id)
      .populate('customer', 'name email phone')
      .populate('items.dish', 'name price');

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    order.status = status;
    
    // Set delivery time if order is delivered
    if (status === 'delivered') {
      order.actualDeliveryTime = new Date();
    }

    await order.save();

    // Emit real-time update
    const io = req.app.get('io');
    io.emit('orderStatusUpdate', {
      orderId: order._id,
      status: order.status,
      customer: order.customer._id
    });

    res.json(order);
  } catch (error) {
    console.error('Update order status error:', error);
    if (error.name === 'CastError') {
      return res.status(404).json({ message: 'Order not found' });
    }
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private
const cancelOrder = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    // Check if user owns the order
    if (order.customer.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Check if order can be cancelled
    if (['delivered', 'cancelled'].includes(order.status)) {
      return res.status(400).json({ message: 'Order cannot be cancelled' });
    }

    order.status = 'cancelled';
    await order.save();

    // Emit real-time update
    const io = req.app.get('io');
    io.to('admin').emit('orderCancelled', order);

    res.json({ message: 'Order cancelled successfully' });
  } catch (error) {
    console.error('Cancel order error:', error);
    if (error.name === 'CastError') {
      return res.status(404).json({ message: 'Order not found' });
    }
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Enable bill splitting for an order
// @route   POST /api/orders/:id/split
// @access  Private
const enableBillSplit = async (req, res) => {
  try {
    const { splitType, totalPeople, participants, splitDetails } = req.body;

    const order = await Order.findById(req.params.id)
      .populate('customer', 'name email phone')
      .populate('items.dish', 'name price');

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    // Check if user owns the order or is admin
    if (order.customer._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Calculate split amounts based on split type
    let calculatedParticipants = [];
    const baseAmount = order.totalAmount;
    const tax = splitDetails?.tax || 0;
    const tip = splitDetails?.tip || 0;
    const serviceFee = splitDetails?.serviceFee || 0;
    const totalWithExtras = baseAmount + tax + tip + serviceFee;

    if (splitType === 'equal') {
      const perPersonAmount = totalWithExtras / totalPeople;
      calculatedParticipants = participants.map(participant => ({
        ...participant,
        amount: parseFloat(perPersonAmount.toFixed(2)),
        paymentStatus: 'pending'
      }));
    } else if (splitType === 'custom') {
      calculatedParticipants = participants.map(participant => ({
        ...participant,
        paymentStatus: 'pending'
      }));
    } else if (splitType === 'by-item') {
      calculatedParticipants = participants.map(participant => {
        const itemTotal = participant.items.reduce((sum, item) => {
          return sum + (item.price * item.quantity);
        }, 0);
        const participantShare = (itemTotal / baseAmount) * totalWithExtras;
        return {
          ...participant,
          amount: parseFloat(participantShare.toFixed(2)),
          paymentStatus: 'pending'
        };
      });
    }

    // Update order with bill split information
    order.billSplit = {
      isEnabled: true,
      totalPeople,
      splitType,
      participants: calculatedParticipants,
      splitDetails: {
        perPersonAmount: splitType === 'equal' ? totalWithExtras / totalPeople : null,
        tax,
        tip,
        serviceFee
      }
    };

    await order.save();

    // Emit real-time update
    const io = req.app.get('io');
    io.emit('billSplitEnabled', {
      orderId: order._id,
      participants: calculatedParticipants
    });

    res.json(order);
  } catch (error) {
    console.error('Enable bill split error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update participant payment status
// @route   PUT /api/orders/:id/split/participant/:participantId
// @access  Private
const updateParticipantPayment = async (req, res) => {
  try {
    const { paymentStatus, paymentMethod } = req.body;

    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    if (!order.billSplit.isEnabled) {
      return res.status(400).json({ message: 'Bill splitting is not enabled for this order' });
    }

    const participant = order.billSplit.participants.id(req.params.participantId);
    if (!participant) {
      return res.status(404).json({ message: 'Participant not found' });
    }

    participant.paymentStatus = paymentStatus;
    participant.paymentMethod = paymentMethod;
    if (paymentStatus === 'paid') {
      participant.paidAt = new Date();
    }

    await order.save();

    // Check if all participants have paid
    const allPaid = order.billSplit.participants.every(p => p.paymentStatus === 'paid');
    if (allPaid && order.paymentStatus === 'pending') {
      order.paymentStatus = 'paid';
      await order.save();
    }

    // Emit real-time update
    const io = req.app.get('io');
    io.emit('participantPaymentUpdate', {
      orderId: order._id,
      participantId: req.params.participantId,
      paymentStatus,
      allPaid
    });

    res.json(order);
  } catch (error) {
    console.error('Update participant payment error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get bill split details
// @route   GET /api/orders/:id/split
// @access  Public (with order access)
const getBillSplitDetails = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('customer', 'name email phone')
      .populate('items.dish', 'name price image')
      .populate('billSplit.participants.items.dish', 'name price');

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    if (!order.billSplit.isEnabled) {
      return res.status(400).json({ message: 'Bill splitting is not enabled for this order' });
    }

    res.json({
      orderNumber: order.orderNumber,
      totalAmount: order.totalAmount,
      billSplit: order.billSplit,
      items: order.items,
      restaurant: {
        name: 'Delicious Restaurant',
        address: '123 Food Street, Culinary City, CC 12345',
        phone: '(*************'
      }
    });
  } catch (error) {
    console.error('Get bill split details error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  createOrder,
  getUserOrders,
  getOrder,
  getAllOrders,
  updateOrderStatus,
  cancelOrder,
  enableBillSplit,
  updateParticipantPayment,
  getBillSplitDetails
};
