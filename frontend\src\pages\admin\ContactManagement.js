import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

const ContactManagement = () => {
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: 'all',
    category: 'all'
  });
  const [selectedContact, setSelectedContact] = useState(null);
  const [replyMessage, setReplyMessage] = useState('');
  const [showReplyForm, setShowReplyForm] = useState(false);

  useEffect(() => {
    fetchContacts();
  }, [filters]);

  const fetchContacts = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.status !== 'all') params.append('status', filters.status);
      if (filters.category !== 'all') params.append('category', filters.category);
      
      const response = await axios.get(`/api/contact/admin/all?${params}`);
      setContacts(response.data.contacts);
    } catch (error) {
      toast.error('Failed to load contacts');
    } finally {
      setLoading(false);
    }
  };

  const updateContactStatus = async (contactId, newStatus, adminNotes = '') => {
    try {
      await axios.put(`/api/contact/${contactId}/status`, { 
        status: newStatus,
        adminNotes 
      });
      toast.success('Contact status updated successfully!');
      fetchContacts();
      if (selectedContact && selectedContact._id === contactId) {
        setSelectedContact({ 
          ...selectedContact, 
          status: newStatus,
          adminNotes 
        });
      }
    } catch (error) {
      toast.error('Failed to update contact status');
    }
  };

  const sendReply = async (contactId) => {
    if (!replyMessage.trim()) {
      toast.error('Please enter a reply message');
      return;
    }

    try {
      await axios.post(`/api/contact/${contactId}/reply`, { 
        replyMessage 
      });
      toast.success('Reply sent successfully!');
      setReplyMessage('');
      setShowReplyForm(false);
      fetchContacts();
      if (selectedContact) {
        setSelectedContact({ 
          ...selectedContact, 
          status: 'replied',
          repliedAt: new Date()
        });
      }
    } catch (error) {
      toast.error('Failed to send reply');
    }
  };

  const deleteContact = async (contactId) => {
    if (window.confirm('Are you sure you want to delete this contact message?')) {
      try {
        await axios.delete(`/api/contact/${contactId}`);
        toast.success('Contact deleted successfully!');
        fetchContacts();
        if (selectedContact && selectedContact._id === contactId) {
          setSelectedContact(null);
        }
      } catch (error) {
        toast.error('Failed to delete contact');
      }
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      new: '#e74c3c',
      read: '#f39c12',
      replied: '#3498db',
      resolved: '#27ae60'
    };
    return colors[status] || '#95a5a6';
  };

  const getCategoryIcon = (category) => {
    const icons = {
      general: '💬',
      complaint: '😞',
      compliment: '😊',
      suggestion: '💡',
      catering: '🍽️',
      other: '📝'
    };
    return icons[category] || '📝';
  };

  if (loading) {
    return <div className="loading">Loading contacts...</div>;
  }

  return (
    <div className="contact-management">
      <div className="container">
        <div className="page-header">
          <h1>Contact Management</h1>
          <div className="filters">
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            >
              <option value="all">All Status</option>
              <option value="new">New</option>
              <option value="read">Read</option>
              <option value="replied">Replied</option>
              <option value="resolved">Resolved</option>
            </select>
            <select
              value={filters.category}
              onChange={(e) => setFilters({ ...filters, category: e.target.value })}
            >
              <option value="all">All Categories</option>
              <option value="general">General</option>
              <option value="complaint">Complaint</option>
              <option value="compliment">Compliment</option>
              <option value="suggestion">Suggestion</option>
              <option value="catering">Catering</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>

        <div className="contacts-layout">
          <div className="contacts-list">
            {contacts.length === 0 ? (
              <div className="no-contacts">No contact messages found</div>
            ) : (
              contacts.map((contact) => (
                <div 
                  key={contact._id} 
                  className={`contact-item ${selectedContact?._id === contact._id ? 'selected' : ''} ${contact.status === 'new' ? 'unread' : ''}`}
                  onClick={() => setSelectedContact(contact)}
                >
                  <div className="contact-header">
                    <div className="contact-info">
                      <span className="category-icon">{getCategoryIcon(contact.category)}</span>
                      <h3>{contact.name}</h3>
                    </div>
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(contact.status) }}
                    >
                      {contact.status}
                    </span>
                  </div>
                  <div className="contact-preview">
                    <p className="subject">{contact.subject}</p>
                    <p className="message-preview">
                      {contact.message.substring(0, 100)}
                      {contact.message.length > 100 ? '...' : ''}
                    </p>
                    <p className="contact-date">
                      {new Date(contact.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="contact-actions">
                    {contact.status === 'new' && (
                      <button
                        className="btn btn-primary btn-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          updateContactStatus(contact._id, 'read');
                        }}
                      >
                        Mark as Read
                      </button>
                    )}
                    {contact.status !== 'replied' && contact.status !== 'resolved' && (
                      <button
                        className="btn btn-secondary btn-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedContact(contact);
                          setShowReplyForm(true);
                        }}
                      >
                        Reply
                      </button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {selectedContact && (
            <div className="contact-details">
              <div className="contact-details-header">
                <h2>Contact Details</h2>
                <button 
                  className="close-btn"
                  onClick={() => setSelectedContact(null)}
                >
                  ×
                </button>
              </div>

              <div className="contact-details-content">
                <div className="detail-section">
                  <h3>Contact Information</h3>
                  <p><strong>Name:</strong> {selectedContact.name}</p>
                  <p><strong>Email:</strong> {selectedContact.email}</p>
                  {selectedContact.phone && (
                    <p><strong>Phone:</strong> {selectedContact.phone}</p>
                  )}
                  <p><strong>Category:</strong> 
                    <span className="category-badge">
                      {getCategoryIcon(selectedContact.category)} {selectedContact.category}
                    </span>
                  </p>
                  <p><strong>Status:</strong> 
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(selectedContact.status) }}
                    >
                      {selectedContact.status}
                    </span>
                  </p>
                  <p><strong>Received:</strong> {new Date(selectedContact.createdAt).toLocaleString()}</p>
                  {selectedContact.repliedAt && (
                    <p><strong>Replied:</strong> {new Date(selectedContact.repliedAt).toLocaleString()}</p>
                  )}
                </div>

                <div className="detail-section">
                  <h3>Subject</h3>
                  <p>{selectedContact.subject}</p>
                </div>

                <div className="detail-section">
                  <h3>Message</h3>
                  <div className="message-content">
                    {selectedContact.message}
                  </div>
                </div>

                {selectedContact.adminNotes && (
                  <div className="detail-section">
                    <h3>Admin Notes</h3>
                    <p>{selectedContact.adminNotes}</p>
                  </div>
                )}

                {showReplyForm && (
                  <div className="detail-section">
                    <h3>Send Reply</h3>
                    <textarea
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                      placeholder="Type your reply here..."
                      rows="6"
                      className="reply-textarea"
                    />
                    <div className="reply-actions">
                      <button
                        className="btn btn-secondary"
                        onClick={() => {
                          setShowReplyForm(false);
                          setReplyMessage('');
                        }}
                      >
                        Cancel
                      </button>
                      <button
                        className="btn btn-primary"
                        onClick={() => sendReply(selectedContact._id)}
                      >
                        Send Reply
                      </button>
                    </div>
                  </div>
                )}

                <div className="detail-actions">
                  <h3>Actions</h3>
                  <div className="action-buttons">
                    {selectedContact.status !== 'replied' && (
                      <button
                        className="btn btn-primary"
                        onClick={() => setShowReplyForm(true)}
                      >
                        Reply to Customer
                      </button>
                    )}
                    
                    <div className="status-buttons">
                      {['new', 'read', 'replied', 'resolved'].map((status) => (
                        <button
                          key={status}
                          className={`btn ${selectedContact.status === status ? 'btn-primary' : 'btn-secondary'}`}
                          onClick={() => updateContactStatus(selectedContact._id, status)}
                          disabled={selectedContact.status === status}
                        >
                          Mark as {status.charAt(0).toUpperCase() + status.slice(1)}
                        </button>
                      ))}
                    </div>

                    <button
                      className="btn btn-danger"
                      onClick={() => deleteContact(selectedContact._id)}
                    >
                      Delete Message
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactManagement;
