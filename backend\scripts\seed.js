const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User');
const Dish = require('../models/Dish');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/restaurant');
    console.log('MongoDB Connected');
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

// Sample data
const sampleUsers = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    phone: '(*************',
    address: {
      street: '123 Admin Street',
      city: 'Admin City',
      state: 'AC',
      zipCode: '12345',
      country: 'USA'
    }
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'customer123',
    role: 'customer',
    phone: '(*************',
    address: {
      street: '456 Customer Ave',
      city: 'Customer City',
      state: 'CC',
      zipCode: '67890',
      country: 'USA'
    }
  }
];

const sampleDishes = [
  // Appetizers
  {
    name: 'Bruschetta Trio',
    description: 'Three varieties of our signature bruschetta with fresh tomatoes, basil, and mozzarella',
    price: 12.99,
    category: 'appetizers',
    image: 'default-dish.jpg',
    ingredients: ['tomatoes', 'basil', 'mozzarella', 'bread', 'olive oil'],
    allergens: ['gluten', 'dairy'],
    dietaryInfo: ['vegetarian'],
    preparationTime: 10,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: true,
    rating: 4.5,
    reviewCount: 23
  },
  {
    name: 'Calamari Rings',
    description: 'Crispy fried squid rings served with marinara sauce and lemon',
    price: 14.99,
    category: 'appetizers',
    image: 'default-dish.jpg',
    ingredients: ['squid', 'flour', 'marinara sauce', 'lemon'],
    allergens: ['gluten'],
    dietaryInfo: [],
    preparationTime: 15,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: false,
    rating: 4.2,
    reviewCount: 18
  },
  {
    name: 'Spinach Artichoke Dip',
    description: 'Creamy blend of spinach, artichokes, and cheese served with tortilla chips',
    price: 11.99,
    category: 'appetizers',
    image: 'default-dish.jpg',
    ingredients: ['spinach', 'artichokes', 'cream cheese', 'tortilla chips'],
    allergens: ['dairy'],
    dietaryInfo: ['vegetarian'],
    preparationTime: 12,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: true,
    rating: 4.7,
    reviewCount: 31
  },

  // Main Courses
  {
    name: 'Grilled Salmon',
    description: 'Fresh Atlantic salmon grilled to perfection with lemon herb butter',
    price: 24.99,
    category: 'main-courses',
    image: 'default-dish.jpg',
    ingredients: ['salmon', 'lemon', 'herbs', 'butter'],
    allergens: ['fish', 'dairy'],
    dietaryInfo: ['gluten-free'],
    preparationTime: 20,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: true,
    rating: 4.8,
    reviewCount: 45
  },
  {
    name: 'Ribeye Steak',
    description: '12oz prime ribeye steak cooked to your preference with garlic mashed potatoes',
    price: 32.99,
    category: 'main-courses',
    image: 'default-dish.jpg',
    ingredients: ['ribeye steak', 'potatoes', 'garlic', 'butter'],
    allergens: ['dairy'],
    dietaryInfo: ['gluten-free'],
    preparationTime: 25,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: true,
    rating: 4.9,
    reviewCount: 67
  },
  {
    name: 'Chicken Parmesan',
    description: 'Breaded chicken breast topped with marinara sauce and melted mozzarella',
    price: 19.99,
    category: 'main-courses',
    image: 'default-dish.jpg',
    ingredients: ['chicken breast', 'breadcrumbs', 'marinara sauce', 'mozzarella'],
    allergens: ['gluten', 'dairy'],
    dietaryInfo: [],
    preparationTime: 22,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: false,
    rating: 4.3,
    reviewCount: 29
  },
  {
    name: 'Vegetarian Pasta',
    description: 'Penne pasta with seasonal vegetables in a creamy alfredo sauce',
    price: 16.99,
    category: 'main-courses',
    image: 'default-dish.jpg',
    ingredients: ['penne pasta', 'zucchini', 'bell peppers', 'alfredo sauce'],
    allergens: ['gluten', 'dairy'],
    dietaryInfo: ['vegetarian'],
    preparationTime: 18,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: false,
    rating: 4.1,
    reviewCount: 22
  },

  // Desserts
  {
    name: 'Chocolate Lava Cake',
    description: 'Warm chocolate cake with a molten center, served with vanilla ice cream',
    price: 8.99,
    category: 'desserts',
    image: 'default-dish.jpg',
    ingredients: ['chocolate', 'flour', 'eggs', 'vanilla ice cream'],
    allergens: ['gluten', 'dairy', 'eggs'],
    dietaryInfo: ['vegetarian'],
    preparationTime: 15,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: true,
    rating: 4.6,
    reviewCount: 38
  },
  {
    name: 'Tiramisu',
    description: 'Classic Italian dessert with layers of coffee-soaked ladyfingers and mascarpone',
    price: 7.99,
    category: 'desserts',
    image: 'default-dish.jpg',
    ingredients: ['ladyfingers', 'coffee', 'mascarpone', 'cocoa powder'],
    allergens: ['gluten', 'dairy', 'eggs'],
    dietaryInfo: ['vegetarian'],
    preparationTime: 5,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: true,
    rating: 4.4,
    reviewCount: 26
  },

  // Beverages
  {
    name: 'Fresh Lemonade',
    description: 'House-made lemonade with fresh lemons and mint',
    price: 3.99,
    category: 'beverages',
    image: 'default-dish.jpg',
    ingredients: ['lemons', 'sugar', 'water', 'mint'],
    allergens: [],
    dietaryInfo: ['vegan', 'gluten-free'],
    preparationTime: 5,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: false,
    rating: 4.0,
    reviewCount: 15
  },
  {
    name: 'Craft Beer Selection',
    description: 'Rotating selection of local craft beers',
    price: 5.99,
    category: 'beverages',
    image: 'default-dish.jpg',
    ingredients: ['hops', 'malt', 'yeast'],
    allergens: ['gluten'],
    dietaryInfo: [],
    preparationTime: 2,
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: true,
    rating: 4.3,
    reviewCount: 42
  }
];

// Seed function
const seedDatabase = async () => {
  try {
    // Clear existing data
    await User.deleteMany({});
    await Dish.deleteMany({});
    
    console.log('Cleared existing data');

    // Create users
    for (const userData of sampleUsers) {
      const user = new User(userData);
      await user.save();
    }
    console.log('Created sample users');

    // Create dishes
    await Dish.insertMany(sampleDishes);
    console.log('Created sample dishes');

    console.log('Database seeded successfully!');
    console.log('\nSample login credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Customer: <EMAIL> / customer123');
    
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    mongoose.connection.close();
  }
};

// Run the seed function
connectDB().then(() => {
  seedDatabase();
});
