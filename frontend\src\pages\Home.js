import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import '../styles/Home.css';

const Home = () => {
  const [featuredDishes, setFeaturedDishes] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFeaturedDishes = async () => {
      try {
        const response = await axios.get('/api/menu?limit=6&sortBy=rating&sortOrder=desc');
        setFeaturedDishes(response.data.dishes);
      } catch (error) {
        console.error('Error fetching featured dishes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedDishes();
  }, []);

  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>Welcome to Delicious Restaurant</h1>
          <p>Experience culinary excellence with our carefully crafted dishes made from the finest ingredients</p>
          <div className="hero-buttons">
            <Link to="/menu" className="btn btn-primary">View Menu</Link>
            <Link to="/reservations" className="btn btn-secondary">Make Reservation</Link>
          </div>
        </div>
        <div className="hero-image">
          <img src="/api/placeholder/600/400" alt="Delicious food" />
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="container">
          <h2>Why Choose Us</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🍽️</div>
              <h3>Fresh Ingredients</h3>
              <p>We source only the freshest, highest quality ingredients for all our dishes</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">👨‍🍳</div>
              <h3>Expert Chefs</h3>
              <p>Our experienced chefs bring passion and creativity to every dish they prepare</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🚚</div>
              <h3>Fast Delivery</h3>
              <p>Quick and reliable delivery service to bring our delicious food to your door</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">⭐</div>
              <h3>5-Star Service</h3>
              <p>Exceptional customer service that makes every dining experience memorable</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Dishes Section */}
      <section className="featured-dishes">
        <div className="container">
          <h2>Featured Dishes</h2>
          {loading ? (
            <div className="loading">Loading featured dishes...</div>
          ) : (
            <div className="dishes-grid">
              {featuredDishes.map((dish) => (
                <div key={dish._id} className="dish-card">
                  <img 
                    src={`http://localhost:5000/uploads/${dish.image}`} 
                    alt={dish.name}
                    onError={(e) => {
                      e.target.src = '/api/placeholder/300/200';
                    }}
                  />
                  <div className="dish-info">
                    <h3>{dish.name}</h3>
                    <p className="dish-description">{dish.description}</p>
                    <div className="dish-meta">
                      <span className="price">${dish.price}</span>
                      <span className="rating">⭐ {dish.rating || 'New'}</span>
                    </div>
                    <div className="dish-tags">
                      {dish.dietaryInfo?.map((tag) => (
                        <span key={tag} className="tag">{tag}</span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          <div className="view-all">
            <Link to="/menu" className="btn btn-primary">View Full Menu</Link>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="about">
        <div className="container">
          <div className="about-content">
            <div className="about-text">
              <h2>About Our Restaurant</h2>
              <p>
                For over 20 years, Delicious Restaurant has been serving the community with 
                exceptional food and outstanding service. Our commitment to quality and 
                customer satisfaction has made us a favorite dining destination.
              </p>
              <p>
                We believe that great food brings people together, and our menu reflects 
                our passion for creating memorable dining experiences. From traditional 
                favorites to innovative new dishes, we have something for everyone.
              </p>
              <Link to="/contact" className="btn btn-primary">Learn More</Link>
            </div>
            <div className="about-image">
              <img src="/api/placeholder/500/400" alt="Restaurant interior" />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <h2>Ready to Experience Amazing Food?</h2>
          <p>Join thousands of satisfied customers who have made us their favorite restaurant</p>
          <div className="cta-buttons">
            <Link to="/menu" className="btn btn-primary">Order Now</Link>
            <Link to="/reservations" className="btn btn-secondary">Book a Table</Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
