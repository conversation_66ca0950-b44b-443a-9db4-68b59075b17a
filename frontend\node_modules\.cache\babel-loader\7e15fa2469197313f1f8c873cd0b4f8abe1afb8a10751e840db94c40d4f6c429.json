{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Restaurant App\\\\frontend\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport '../styles/Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Delicious Restaurant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Experience the finest dining with our carefully crafted dishes made from the freshest ingredients.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"social-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            \"aria-label\": \"Facebook\",\n            children: \"\\uD83D\\uDCD8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            \"aria-label\": \"Instagram\",\n            children: \"\\uD83D\\uDCF7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            \"aria-label\": \"Twitter\",\n            children: \"\\uD83D\\uDC26\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            \"aria-label\": \"YouTube\",\n            children: \"\\uD83D\\uDCFA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Quick Links\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/menu\",\n              children: \"Menu\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/reservations\",\n              children: \"Reservations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Contact Info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\uD83D\\uDCCD 123 Food Street, Culinary City, CC 12345\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\uD83D\\uDCDE (555) 123-4567\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u2709\\uFE0F <EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Opening Hours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hours\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Monday - Thursday:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 16\n            }, this), \" 11:00 AM - 10:00 PM\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Friday - Saturday:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 16\n            }, this), \" 11:00 AM - 11:00 PM\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Sunday:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 16\n            }, this), \" 12:00 PM - 9:00 PM\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-bottom\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2024 Delicious Restaurant. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-links\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/privacy\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/terms\",\n            children: \"Terms of Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "to", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport '../styles/Footer.css';\n\nconst Footer = () => {\n  return (\n    <footer className=\"footer\">\n      <div className=\"footer-container\">\n        <div className=\"footer-section\">\n          <h3>Delicious Restaurant</h3>\n          <p>Experience the finest dining with our carefully crafted dishes made from the freshest ingredients.</p>\n          <div className=\"social-links\">\n            <a href=\"#\" aria-label=\"Facebook\">📘</a>\n            <a href=\"#\" aria-label=\"Instagram\">📷</a>\n            <a href=\"#\" aria-label=\"Twitter\">🐦</a>\n            <a href=\"#\" aria-label=\"YouTube\">📺</a>\n          </div>\n        </div>\n        \n        <div className=\"footer-section\">\n          <h4>Quick Links</h4>\n          <ul>\n            <li><Link to=\"/\">Home</Link></li>\n            <li><Link to=\"/menu\">Menu</Link></li>\n            <li><Link to=\"/reservations\">Reservations</Link></li>\n            <li><Link to=\"/contact\">Contact</Link></li>\n          </ul>\n        </div>\n        \n        <div className=\"footer-section\">\n          <h4>Contact Info</h4>\n          <div className=\"contact-info\">\n            <p>📍 123 Food Street, Culinary City, CC 12345</p>\n            <p>📞 (555) 123-4567</p>\n            <p>✉️ <EMAIL></p>\n          </div>\n        </div>\n        \n        <div className=\"footer-section\">\n          <h4>Opening Hours</h4>\n          <div className=\"hours\">\n            <p><strong>Monday - Thursday:</strong> 11:00 AM - 10:00 PM</p>\n            <p><strong>Friday - Saturday:</strong> 11:00 AM - 11:00 PM</p>\n            <p><strong>Sunday:</strong> 12:00 PM - 9:00 PM</p>\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"footer-bottom\">\n        <div className=\"footer-container\">\n          <p>&copy; 2024 Delicious Restaurant. All rights reserved.</p>\n          <div className=\"footer-links\">\n            <Link to=\"/privacy\">Privacy Policy</Link>\n            <Link to=\"/terms\">Terms of Service</Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACxBH,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BH,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAAG,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BP,OAAA;UAAAG,QAAA,EAAG;QAAkG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzGP,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YAAGQ,IAAI,EAAC,GAAG;YAAC,cAAW,UAAU;YAAAL,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxCP,OAAA;YAAGQ,IAAI,EAAC,GAAG;YAAC,cAAW,WAAW;YAAAL,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzCP,OAAA;YAAGQ,IAAI,EAAC,GAAG;YAAC,cAAW,SAAS;YAAAL,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvCP,OAAA;YAAGQ,IAAI,EAAC,GAAG;YAAC,cAAW,SAAS;YAAAL,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAAG,QAAA,EAAI;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpBP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;cAACW,EAAE,EAAC,GAAG;cAAAN,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjCP,OAAA;YAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;cAACW,EAAE,EAAC,OAAO;cAAAN,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCP,OAAA;YAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;cAACW,EAAE,EAAC,eAAe;cAAAN,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDP,OAAA;YAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;cAACW,EAAE,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAAG,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBP,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YAAAG,QAAA,EAAG;UAA2C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClDP,OAAA;YAAAG,QAAA,EAAG;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxBP,OAAA;YAAAG,QAAA,EAAG;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAAG,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBP,OAAA;UAAKE,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACpBH,OAAA;YAAAG,QAAA,gBAAGH,OAAA;cAAAG,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wBAAoB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9DP,OAAA;YAAAG,QAAA,gBAAGH,OAAA;cAAAG,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wBAAoB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9DP,OAAA;YAAAG,QAAA,gBAAGH,OAAA;cAAAG,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uBAAmB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BH,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BH,OAAA;UAAAG,QAAA,EAAG;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7DP,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA,CAACF,IAAI;YAACW,EAAE,EAAC,UAAU;YAAAN,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCP,OAAA,CAACF,IAAI;YAACW,EAAE,EAAC,QAAQ;YAAAN,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACG,EAAA,GAvDIT,MAAM;AAyDZ,eAAeA,MAAM;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}