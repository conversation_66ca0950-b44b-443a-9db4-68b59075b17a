const mongoose = require('mongoose');

const orderItemSchema = new mongoose.Schema({
  dish: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Dish',
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1']
  },
  price: {
    type: Number,
    required: true,
    min: [0, 'Price cannot be negative']
  },
  specialInstructions: {
    type: String,
    maxlength: [200, 'Special instructions cannot be more than 200 characters']
  }
});

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [orderItemSchema],
  totalAmount: {
    type: Number,
    required: true,
    min: [0, 'Total amount cannot be negative']
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'],
    default: 'pending'
  },
  orderType: {
    type: String,
    enum: ['delivery', 'pickup', 'dine-in'],
    required: true
  },
  deliveryAddress: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
    phone: String
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'online'],
    required: true
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  estimatedDeliveryTime: {
    type: Date
  },
  actualDeliveryTime: {
    type: Date
  },
  notes: {
    type: String,
    maxlength: [500, 'Notes cannot be more than 500 characters']
  },
  billSplit: {
    isEnabled: {
      type: Boolean,
      default: false
    },
    totalPeople: {
      type: Number,
      min: [1, 'Total people must be at least 1'],
      max: [20, 'Cannot split bill among more than 20 people']
    },
    splitType: {
      type: String,
      enum: ['equal', 'custom', 'by-item'],
      default: 'equal'
    },
    participants: [{
      name: {
        type: String,
        required: true,
        trim: true
      },
      email: {
        type: String,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please provide a valid email']
      },
      phone: {
        type: String,
        match: [/^\+?[\d\s-()]+$/, 'Please provide a valid phone number']
      },
      amount: {
        type: Number,
        min: [0, 'Amount cannot be negative']
      },
      items: [{
        dish: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Dish'
        },
        quantity: Number,
        price: Number
      }],
      paymentStatus: {
        type: String,
        enum: ['pending', 'paid', 'failed'],
        default: 'pending'
      },
      paymentMethod: {
        type: String,
        enum: ['cash', 'card', 'online', 'venmo', 'paypal']
      },
      paidAt: Date
    }],
    splitDetails: {
      perPersonAmount: Number,
      tax: Number,
      tip: Number,
      serviceFee: Number
    }
  }
}, {
  timestamps: true
});

// Generate order number before saving
orderSchema.pre('save', async function(next) {
  if (!this.orderNumber) {
    const count = await mongoose.model('Order').countDocuments();
    this.orderNumber = `ORD${Date.now()}${(count + 1).toString().padStart(3, '0')}`;
  }
  next();
});

module.exports = mongoose.model('Order', orderSchema);
