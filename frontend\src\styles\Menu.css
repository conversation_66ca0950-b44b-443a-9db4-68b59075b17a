.menu-page {
  padding: 40px 0;
  min-height: 100vh;
}

.menu-header {
  text-align: center;
  margin-bottom: 40px;
}

.menu-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.menu-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* Filters */
.menu-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 40px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.filter-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 120px;
}

.price-inputs {
  display: flex;
  gap: 10px;
}

.price-inputs input {
  width: 80px;
}

/* Menu Grid */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.menu-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.menu-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.menu-item-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.menu-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.menu-item:hover .menu-item-image img {
  transform: scale(1.05);
}

.popular-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.menu-item-content {
  padding: 20px;
}

.menu-item-content h3 {
  font-size: 1.3rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.description {
  color: #7f8c8d;
  margin-bottom: 15px;
  line-height: 1.5;
  font-size: 14px;
}

.dish-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
  gap: 10px;
}

.price {
  font-size: 1.2rem;
  font-weight: bold;
  color: #e74c3c;
}

.prep-time,
.rating {
  font-size: 14px;
  color: #7f8c8d;
}

.dietary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 10px;
}

.dietary-tag {
  background: #3498db;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.spice-level {
  font-size: 14px;
  color: #e67e22;
  margin-bottom: 15px;
}

.menu-item-actions {
  margin-top: 15px;
}

.add-to-cart-btn {
  width: 100%;
  padding: 12px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-to-cart-btn:hover:not(:disabled) {
  background: #c0392b;
}

.add-to-cart-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 40px;
}

.pagination-btn {
  padding: 10px 20px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #c0392b;
}

.pagination-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
}

.pagination-info {
  font-weight: 600;
  color: #2c3e50;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 60px 20px;
  font-size: 18px;
  color: #7f8c8d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu-filters {
    flex-direction: column;
    gap: 15px;
  }

  .filter-group {
    width: 100%;
  }

  .filter-group select,
  .filter-group input {
    width: 100%;
  }

  .menu-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .dish-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .pagination {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .menu-page {
    padding: 20px 0;
  }

  .menu-header h1 {
    font-size: 2rem;
  }

  .menu-filters {
    margin: 0 -10px 30px;
    border-radius: 0;
  }

  .menu-item-content {
    padding: 15px;
  }

  .menu-item-content h3 {
    font-size: 1.1rem;
  }
}
