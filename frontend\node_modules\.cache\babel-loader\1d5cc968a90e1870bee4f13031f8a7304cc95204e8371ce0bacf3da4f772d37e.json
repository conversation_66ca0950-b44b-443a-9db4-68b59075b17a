{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Restaurant App\\\\frontend\\\\src\\\\components\\\\BillSplit.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\nimport '../styles/BillSplit.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BillSplit = ({\n  orderId,\n  onClose,\n  orderData\n}) => {\n  _s();\n  const [splitType, setSplitType] = useState('equal');\n  const [totalPeople, setTotalPeople] = useState(2);\n  const [participants, setParticipants] = useState([]);\n  const [splitDetails, setSplitDetails] = useState({\n    tax: 0,\n    tip: 0,\n    serviceFee: 0\n  });\n  const [loading, setLoading] = useState(false);\n  const [billSplitData, setBillSplitData] = useState(null);\n  useEffect(() => {\n    // Initialize participants based on total people\n    const newParticipants = Array.from({\n      length: totalPeople\n    }, (_, index) => ({\n      name: `Person ${index + 1}`,\n      email: '',\n      phone: '',\n      amount: 0,\n      items: []\n    }));\n    setParticipants(newParticipants);\n  }, [totalPeople]);\n  useEffect(() => {\n    var _orderData$billSplit;\n    // Check if bill split is already enabled\n    if (orderData !== null && orderData !== void 0 && (_orderData$billSplit = orderData.billSplit) !== null && _orderData$billSplit !== void 0 && _orderData$billSplit.isEnabled) {\n      setBillSplitData(orderData.billSplit);\n    }\n  }, [orderData]);\n  const calculateSplit = () => {\n    const baseAmount = orderData.totalAmount;\n    const totalWithExtras = baseAmount + splitDetails.tax + splitDetails.tip + splitDetails.serviceFee;\n    if (splitType === 'equal') {\n      const perPersonAmount = totalWithExtras / totalPeople;\n      return participants.map(participant => ({\n        ...participant,\n        amount: parseFloat(perPersonAmount.toFixed(2))\n      }));\n    } else if (splitType === 'custom') {\n      return participants;\n    } else if (splitType === 'by-item') {\n      return participants.map(participant => {\n        const itemTotal = participant.items.reduce((sum, item) => {\n          return sum + item.price * item.quantity;\n        }, 0);\n        const participantShare = itemTotal / baseAmount * totalWithExtras;\n        return {\n          ...participant,\n          amount: parseFloat(participantShare.toFixed(2))\n        };\n      });\n    }\n    return participants;\n  };\n  const handleParticipantChange = (index, field, value) => {\n    const updatedParticipants = [...participants];\n    updatedParticipants[index] = {\n      ...updatedParticipants[index],\n      [field]: value\n    };\n    setParticipants(updatedParticipants);\n  };\n  const handleItemAssignment = (participantIndex, itemIndex, quantity) => {\n    const updatedParticipants = [...participants];\n    const item = orderData.items[itemIndex];\n    const existingItemIndex = updatedParticipants[participantIndex].items.findIndex(i => i.dish === item.dish._id);\n    if (existingItemIndex >= 0) {\n      if (quantity === 0) {\n        updatedParticipants[participantIndex].items.splice(existingItemIndex, 1);\n      } else {\n        updatedParticipants[participantIndex].items[existingItemIndex].quantity = quantity;\n      }\n    } else if (quantity > 0) {\n      updatedParticipants[participantIndex].items.push({\n        dish: item.dish._id,\n        quantity,\n        price: item.price\n      });\n    }\n    setParticipants(updatedParticipants);\n  };\n  const handleSubmit = async () => {\n    setLoading(true);\n    try {\n      const calculatedParticipants = calculateSplit();\n      const splitData = {\n        splitType,\n        totalPeople,\n        participants: calculatedParticipants,\n        splitDetails\n      };\n      await axios.post(`/api/orders/${orderId}/split`, splitData);\n      toast.success('Bill split enabled successfully!');\n\n      // Refresh bill split data\n      const response = await axios.get(`/api/orders/${orderId}/split`);\n      setBillSplitData(response.data.billSplit);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to enable bill split');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updatePaymentStatus = async (participantId, paymentStatus, paymentMethod) => {\n    try {\n      await axios.put(`/api/orders/${orderId}/split/participant/${participantId}`, {\n        paymentStatus,\n        paymentMethod\n      });\n      toast.success('Payment status updated!');\n\n      // Refresh bill split data\n      const response = await axios.get(`/api/orders/${orderId}/split`);\n      setBillSplitData(response.data.billSplit);\n    } catch (error) {\n      toast.error('Failed to update payment status');\n    }\n  };\n  const generateShareableLink = () => {\n    const link = `${window.location.origin}/split/${orderId}`;\n    navigator.clipboard.writeText(link);\n    toast.success('Shareable link copied to clipboard!');\n  };\n  if (billSplitData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bill-split-modal\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bill-split-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bill-split-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [\"Bill Split - Order #\", orderData.orderNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-btn\",\n            onClick: onClose,\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"split-summary\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"total-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Total Amount: $\", orderData.totalAmount]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Split Type: \", billSplitData.splitType]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Total People: \", billSplitData.totalPeople]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"share-btn\",\n            onClick: generateShareableLink,\n            children: \"\\uD83D\\uDCCB Copy Shareable Link\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"participants-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Participants\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), billSplitData.participants.map((participant, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"participant-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"participant-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: participant.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Amount: $\", participant.amount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Status:\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `status ${participant.paymentStatus}`,\n                  children: participant.paymentStatus\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), participant.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Email: \", participant.email]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 41\n              }, this), participant.phone && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Phone: \", participant.phone]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-actions\",\n              children: [participant.paymentStatus === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-methods\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => updatePaymentStatus(participant._id, 'paid', 'cash'),\n                  className: \"payment-btn cash\",\n                  children: \"Mark as Paid (Cash)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => updatePaymentStatus(participant._id, 'paid', 'card'),\n                  className: \"payment-btn card\",\n                  children: \"Mark as Paid (Card)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this), participant.paymentStatus === 'paid' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"paid-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"\\u2705 Paid via \", participant.paymentMethod]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Paid at: \", new Date(participant.paidAt).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bill-split-modal\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bill-split-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bill-split-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"Split Bill - Order #\", orderData.orderNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-btn\",\n          onClick: onClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"split-setup\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-summary\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Order Total: $\", orderData.totalAmount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"items-list\",\n            children: orderData.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [item.dish.name, \" x\", item.quantity]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"$\", (item.price * item.quantity).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"split-options\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Split Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: splitType,\n              onChange: e => setSplitType(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"equal\",\n                children: \"Equal Split\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"custom\",\n                children: \"Custom Amounts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"by-item\",\n                children: \"By Items\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Number of People:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              min: \"2\",\n              max: \"20\",\n              value: totalPeople,\n              onChange: e => setTotalPeople(parseInt(e.target.value))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"additional-costs\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Additional Costs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cost-inputs\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Tax:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  value: splitDetails.tax,\n                  onChange: e => setSplitDetails({\n                    ...splitDetails,\n                    tax: parseFloat(e.target.value) || 0\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Tip:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  value: splitDetails.tip,\n                  onChange: e => setSplitDetails({\n                    ...splitDetails,\n                    tip: parseFloat(e.target.value) || 0\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Service Fee:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  value: splitDetails.serviceFee,\n                  onChange: e => setSplitDetails({\n                    ...splitDetails,\n                    serviceFee: parseFloat(e.target.value) || 0\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"participants-setup\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Participants\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), participants.map((participant, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"participant-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"participant-basic\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Name\",\n                value: participant.name,\n                onChange: e => handleParticipantChange(index, 'name', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                placeholder: \"Email (optional)\",\n                value: participant.email,\n                onChange: e => handleParticipantChange(index, 'email', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"tel\",\n                placeholder: \"Phone (optional)\",\n                value: participant.phone,\n                onChange: e => handleParticipantChange(index, 'phone', e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), splitType === 'custom' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"custom-amount\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Amount:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.01\",\n                value: participant.amount,\n                onChange: e => handleParticipantChange(index, 'amount', parseFloat(e.target.value) || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 19\n            }, this), splitType === 'by-item' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-assignment\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Assign Items:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 21\n              }, this), orderData.items.map((item, itemIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"item-assign\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [item.dish.name, \" ($\", item.price, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  min: \"0\",\n                  max: item.quantity,\n                  placeholder: \"Qty\",\n                  onChange: e => handleItemAssignment(index, itemIndex, parseInt(e.target.value) || 0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 25\n                }, this)]\n              }, itemIndex, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 19\n            }, this), splitType === 'equal' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"calculated-amount\",\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [\"Amount: $\", ((orderData.totalAmount + splitDetails.tax + splitDetails.tip + splitDetails.serviceFee) / totalPeople).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"split-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: onClose,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: handleSubmit,\n            disabled: loading,\n            children: loading ? 'Setting up...' : 'Enable Bill Split'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(BillSplit, \"71KQpaBr40oJdBlb/zS79cR/Rpk=\");\n_c = BillSplit;\nexport default BillSplit;\nvar _c;\n$RefreshReg$(_c, \"BillSplit\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "toast", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "orderId", "onClose", "orderData", "_s", "splitType", "setSplitType", "totalPeople", "setTotalPeople", "participants", "setParticipants", "splitDetails", "setSplitDetails", "tax", "tip", "serviceFee", "loading", "setLoading", "billSplitData", "setBillSplitData", "newParticipants", "Array", "from", "length", "_", "index", "name", "email", "phone", "amount", "items", "_orderData$billSplit", "billSplit", "isEnabled", "calculateSplit", "baseAmount", "totalAmount", "totalWithExtras", "perPersonAmount", "map", "participant", "parseFloat", "toFixed", "itemTotal", "reduce", "sum", "item", "price", "quantity", "participantShare", "handleParticipantChange", "field", "value", "updatedParticipants", "handleItemAssignment", "participantIndex", "itemIndex", "existingItemIndex", "findIndex", "i", "dish", "_id", "splice", "push", "handleSubmit", "calculatedParticipants", "splitData", "post", "success", "response", "get", "data", "error", "_error$response", "_error$response$data", "message", "updatePaymentStatus", "participantId", "paymentStatus", "paymentMethod", "put", "generateShareableLink", "link", "window", "location", "origin", "navigator", "clipboard", "writeText", "className", "children", "orderNumber", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "Date", "paidAt", "toLocaleString", "onChange", "e", "target", "type", "min", "max", "parseInt", "step", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/src/components/BillSplit.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\nimport '../styles/BillSplit.css';\n\nconst BillSplit = ({ orderId, onClose, orderData }) => {\n  const [splitType, setSplitType] = useState('equal');\n  const [totalPeople, setTotalPeople] = useState(2);\n  const [participants, setParticipants] = useState([]);\n  const [splitDetails, setSplitDetails] = useState({\n    tax: 0,\n    tip: 0,\n    serviceFee: 0\n  });\n  const [loading, setLoading] = useState(false);\n  const [billSplitData, setBillSplitData] = useState(null);\n\n  useEffect(() => {\n    // Initialize participants based on total people\n    const newParticipants = Array.from({ length: totalPeople }, (_, index) => ({\n      name: `Person ${index + 1}`,\n      email: '',\n      phone: '',\n      amount: 0,\n      items: []\n    }));\n    setParticipants(newParticipants);\n  }, [totalPeople]);\n\n  useEffect(() => {\n    // Check if bill split is already enabled\n    if (orderData?.billSplit?.isEnabled) {\n      setBillSplitData(orderData.billSplit);\n    }\n  }, [orderData]);\n\n  const calculateSplit = () => {\n    const baseAmount = orderData.totalAmount;\n    const totalWithExtras = baseAmount + splitDetails.tax + splitDetails.tip + splitDetails.serviceFee;\n\n    if (splitType === 'equal') {\n      const perPersonAmount = totalWithExtras / totalPeople;\n      return participants.map(participant => ({\n        ...participant,\n        amount: parseFloat(perPersonAmount.toFixed(2))\n      }));\n    } else if (splitType === 'custom') {\n      return participants;\n    } else if (splitType === 'by-item') {\n      return participants.map(participant => {\n        const itemTotal = participant.items.reduce((sum, item) => {\n          return sum + (item.price * item.quantity);\n        }, 0);\n        const participantShare = (itemTotal / baseAmount) * totalWithExtras;\n        return {\n          ...participant,\n          amount: parseFloat(participantShare.toFixed(2))\n        };\n      });\n    }\n    return participants;\n  };\n\n  const handleParticipantChange = (index, field, value) => {\n    const updatedParticipants = [...participants];\n    updatedParticipants[index] = {\n      ...updatedParticipants[index],\n      [field]: value\n    };\n    setParticipants(updatedParticipants);\n  };\n\n  const handleItemAssignment = (participantIndex, itemIndex, quantity) => {\n    const updatedParticipants = [...participants];\n    const item = orderData.items[itemIndex];\n    \n    const existingItemIndex = updatedParticipants[participantIndex].items.findIndex(\n      i => i.dish === item.dish._id\n    );\n\n    if (existingItemIndex >= 0) {\n      if (quantity === 0) {\n        updatedParticipants[participantIndex].items.splice(existingItemIndex, 1);\n      } else {\n        updatedParticipants[participantIndex].items[existingItemIndex].quantity = quantity;\n      }\n    } else if (quantity > 0) {\n      updatedParticipants[participantIndex].items.push({\n        dish: item.dish._id,\n        quantity,\n        price: item.price\n      });\n    }\n\n    setParticipants(updatedParticipants);\n  };\n\n  const handleSubmit = async () => {\n    setLoading(true);\n    try {\n      const calculatedParticipants = calculateSplit();\n      \n      const splitData = {\n        splitType,\n        totalPeople,\n        participants: calculatedParticipants,\n        splitDetails\n      };\n\n      await axios.post(`/api/orders/${orderId}/split`, splitData);\n      toast.success('Bill split enabled successfully!');\n      \n      // Refresh bill split data\n      const response = await axios.get(`/api/orders/${orderId}/split`);\n      setBillSplitData(response.data.billSplit);\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Failed to enable bill split');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updatePaymentStatus = async (participantId, paymentStatus, paymentMethod) => {\n    try {\n      await axios.put(`/api/orders/${orderId}/split/participant/${participantId}`, {\n        paymentStatus,\n        paymentMethod\n      });\n      toast.success('Payment status updated!');\n      \n      // Refresh bill split data\n      const response = await axios.get(`/api/orders/${orderId}/split`);\n      setBillSplitData(response.data.billSplit);\n    } catch (error) {\n      toast.error('Failed to update payment status');\n    }\n  };\n\n  const generateShareableLink = () => {\n    const link = `${window.location.origin}/split/${orderId}`;\n    navigator.clipboard.writeText(link);\n    toast.success('Shareable link copied to clipboard!');\n  };\n\n  if (billSplitData) {\n    return (\n      <div className=\"bill-split-modal\">\n        <div className=\"bill-split-content\">\n          <div className=\"bill-split-header\">\n            <h2>Bill Split - Order #{orderData.orderNumber}</h2>\n            <button className=\"close-btn\" onClick={onClose}>×</button>\n          </div>\n\n          <div className=\"split-summary\">\n            <div className=\"total-info\">\n              <h3>Total Amount: ${orderData.totalAmount}</h3>\n              <p>Split Type: {billSplitData.splitType}</p>\n              <p>Total People: {billSplitData.totalPeople}</p>\n            </div>\n            \n            <button className=\"share-btn\" onClick={generateShareableLink}>\n              📋 Copy Shareable Link\n            </button>\n          </div>\n\n          <div className=\"participants-list\">\n            <h3>Participants</h3>\n            {billSplitData.participants.map((participant, index) => (\n              <div key={index} className=\"participant-card\">\n                <div className=\"participant-info\">\n                  <h4>{participant.name}</h4>\n                  <p>Amount: ${participant.amount}</p>\n                  <p>Status: \n                    <span className={`status ${participant.paymentStatus}`}>\n                      {participant.paymentStatus}\n                    </span>\n                  </p>\n                  {participant.email && <p>Email: {participant.email}</p>}\n                  {participant.phone && <p>Phone: {participant.phone}</p>}\n                </div>\n                \n                <div className=\"payment-actions\">\n                  {participant.paymentStatus === 'pending' && (\n                    <div className=\"payment-methods\">\n                      <button \n                        onClick={() => updatePaymentStatus(participant._id, 'paid', 'cash')}\n                        className=\"payment-btn cash\"\n                      >\n                        Mark as Paid (Cash)\n                      </button>\n                      <button \n                        onClick={() => updatePaymentStatus(participant._id, 'paid', 'card')}\n                        className=\"payment-btn card\"\n                      >\n                        Mark as Paid (Card)\n                      </button>\n                    </div>\n                  )}\n                  {participant.paymentStatus === 'paid' && (\n                    <div className=\"paid-info\">\n                      <p>✅ Paid via {participant.paymentMethod}</p>\n                      <p>Paid at: {new Date(participant.paidAt).toLocaleString()}</p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bill-split-modal\">\n      <div className=\"bill-split-content\">\n        <div className=\"bill-split-header\">\n          <h2>Split Bill - Order #{orderData.orderNumber}</h2>\n          <button className=\"close-btn\" onClick={onClose}>×</button>\n        </div>\n\n        <div className=\"split-setup\">\n          <div className=\"order-summary\">\n            <h3>Order Total: ${orderData.totalAmount}</h3>\n            <div className=\"items-list\">\n              {orderData.items.map((item, index) => (\n                <div key={index} className=\"item\">\n                  <span>{item.dish.name} x{item.quantity}</span>\n                  <span>${(item.price * item.quantity).toFixed(2)}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"split-options\">\n            <div className=\"form-group\">\n              <label>Split Type:</label>\n              <select value={splitType} onChange={(e) => setSplitType(e.target.value)}>\n                <option value=\"equal\">Equal Split</option>\n                <option value=\"custom\">Custom Amounts</option>\n                <option value=\"by-item\">By Items</option>\n              </select>\n            </div>\n\n            <div className=\"form-group\">\n              <label>Number of People:</label>\n              <input\n                type=\"number\"\n                min=\"2\"\n                max=\"20\"\n                value={totalPeople}\n                onChange={(e) => setTotalPeople(parseInt(e.target.value))}\n              />\n            </div>\n\n            <div className=\"additional-costs\">\n              <h4>Additional Costs</h4>\n              <div className=\"cost-inputs\">\n                <div className=\"form-group\">\n                  <label>Tax:</label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={splitDetails.tax}\n                    onChange={(e) => setSplitDetails({\n                      ...splitDetails,\n                      tax: parseFloat(e.target.value) || 0\n                    })}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Tip:</label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={splitDetails.tip}\n                    onChange={(e) => setSplitDetails({\n                      ...splitDetails,\n                      tip: parseFloat(e.target.value) || 0\n                    })}\n                  />\n                </div>\n                <div className=\"form-group\">\n                  <label>Service Fee:</label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={splitDetails.serviceFee}\n                    onChange={(e) => setSplitDetails({\n                      ...splitDetails,\n                      serviceFee: parseFloat(e.target.value) || 0\n                    })}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"participants-setup\">\n            <h3>Participants</h3>\n            {participants.map((participant, index) => (\n              <div key={index} className=\"participant-form\">\n                <div className=\"participant-basic\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"Name\"\n                    value={participant.name}\n                    onChange={(e) => handleParticipantChange(index, 'name', e.target.value)}\n                  />\n                  <input\n                    type=\"email\"\n                    placeholder=\"Email (optional)\"\n                    value={participant.email}\n                    onChange={(e) => handleParticipantChange(index, 'email', e.target.value)}\n                  />\n                  <input\n                    type=\"tel\"\n                    placeholder=\"Phone (optional)\"\n                    value={participant.phone}\n                    onChange={(e) => handleParticipantChange(index, 'phone', e.target.value)}\n                  />\n                </div>\n\n                {splitType === 'custom' && (\n                  <div className=\"custom-amount\">\n                    <label>Amount:</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      value={participant.amount}\n                      onChange={(e) => handleParticipantChange(index, 'amount', parseFloat(e.target.value) || 0)}\n                    />\n                  </div>\n                )}\n\n                {splitType === 'by-item' && (\n                  <div className=\"item-assignment\">\n                    <h4>Assign Items:</h4>\n                    {orderData.items.map((item, itemIndex) => (\n                      <div key={itemIndex} className=\"item-assign\">\n                        <span>{item.dish.name} (${item.price})</span>\n                        <input\n                          type=\"number\"\n                          min=\"0\"\n                          max={item.quantity}\n                          placeholder=\"Qty\"\n                          onChange={(e) => handleItemAssignment(index, itemIndex, parseInt(e.target.value) || 0)}\n                        />\n                      </div>\n                    ))}\n                  </div>\n                )}\n\n                {splitType === 'equal' && (\n                  <div className=\"calculated-amount\">\n                    <strong>Amount: ${((orderData.totalAmount + splitDetails.tax + splitDetails.tip + splitDetails.serviceFee) / totalPeople).toFixed(2)}</strong>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          <div className=\"split-actions\">\n            <button className=\"btn btn-secondary\" onClick={onClose}>\n              Cancel\n            </button>\n            <button \n              className=\"btn btn-primary\" \n              onClick={handleSubmit}\n              disabled={loading}\n            >\n              {loading ? 'Setting up...' : 'Enable Bill Split'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BillSplit;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,SAAS,GAAGA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC;IAC/CmB,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAExDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMyB,eAAe,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAEhB;IAAY,CAAC,EAAE,CAACiB,CAAC,EAAEC,KAAK,MAAM;MACzEC,IAAI,EAAE,UAAUD,KAAK,GAAG,CAAC,EAAE;MAC3BE,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE;IACT,CAAC,CAAC,CAAC;IACHpB,eAAe,CAACU,eAAe,CAAC;EAClC,CAAC,EAAE,CAACb,WAAW,CAAC,CAAC;EAEjBZ,SAAS,CAAC,MAAM;IAAA,IAAAoC,oBAAA;IACd;IACA,IAAI5B,SAAS,aAATA,SAAS,gBAAA4B,oBAAA,GAAT5B,SAAS,CAAE6B,SAAS,cAAAD,oBAAA,eAApBA,oBAAA,CAAsBE,SAAS,EAAE;MACnCd,gBAAgB,CAAChB,SAAS,CAAC6B,SAAS,CAAC;IACvC;EACF,CAAC,EAAE,CAAC7B,SAAS,CAAC,CAAC;EAEf,MAAM+B,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,UAAU,GAAGhC,SAAS,CAACiC,WAAW;IACxC,MAAMC,eAAe,GAAGF,UAAU,GAAGxB,YAAY,CAACE,GAAG,GAAGF,YAAY,CAACG,GAAG,GAAGH,YAAY,CAACI,UAAU;IAElG,IAAIV,SAAS,KAAK,OAAO,EAAE;MACzB,MAAMiC,eAAe,GAAGD,eAAe,GAAG9B,WAAW;MACrD,OAAOE,YAAY,CAAC8B,GAAG,CAACC,WAAW,KAAK;QACtC,GAAGA,WAAW;QACdX,MAAM,EAAEY,UAAU,CAACH,eAAe,CAACI,OAAO,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIrC,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAOI,YAAY;IACrB,CAAC,MAAM,IAAIJ,SAAS,KAAK,SAAS,EAAE;MAClC,OAAOI,YAAY,CAAC8B,GAAG,CAACC,WAAW,IAAI;QACrC,MAAMG,SAAS,GAAGH,WAAW,CAACV,KAAK,CAACc,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;UACxD,OAAOD,GAAG,GAAIC,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACE,QAAS;QAC3C,CAAC,EAAE,CAAC,CAAC;QACL,MAAMC,gBAAgB,GAAIN,SAAS,GAAGR,UAAU,GAAIE,eAAe;QACnE,OAAO;UACL,GAAGG,WAAW;UACdX,MAAM,EAAEY,UAAU,CAACQ,gBAAgB,CAACP,OAAO,CAAC,CAAC,CAAC;QAChD,CAAC;MACH,CAAC,CAAC;IACJ;IACA,OAAOjC,YAAY;EACrB,CAAC;EAED,MAAMyC,uBAAuB,GAAGA,CAACzB,KAAK,EAAE0B,KAAK,EAAEC,KAAK,KAAK;IACvD,MAAMC,mBAAmB,GAAG,CAAC,GAAG5C,YAAY,CAAC;IAC7C4C,mBAAmB,CAAC5B,KAAK,CAAC,GAAG;MAC3B,GAAG4B,mBAAmB,CAAC5B,KAAK,CAAC;MAC7B,CAAC0B,KAAK,GAAGC;IACX,CAAC;IACD1C,eAAe,CAAC2C,mBAAmB,CAAC;EACtC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAACC,gBAAgB,EAAEC,SAAS,EAAER,QAAQ,KAAK;IACtE,MAAMK,mBAAmB,GAAG,CAAC,GAAG5C,YAAY,CAAC;IAC7C,MAAMqC,IAAI,GAAG3C,SAAS,CAAC2B,KAAK,CAAC0B,SAAS,CAAC;IAEvC,MAAMC,iBAAiB,GAAGJ,mBAAmB,CAACE,gBAAgB,CAAC,CAACzB,KAAK,CAAC4B,SAAS,CAC7EC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKd,IAAI,CAACc,IAAI,CAACC,GAC5B,CAAC;IAED,IAAIJ,iBAAiB,IAAI,CAAC,EAAE;MAC1B,IAAIT,QAAQ,KAAK,CAAC,EAAE;QAClBK,mBAAmB,CAACE,gBAAgB,CAAC,CAACzB,KAAK,CAACgC,MAAM,CAACL,iBAAiB,EAAE,CAAC,CAAC;MAC1E,CAAC,MAAM;QACLJ,mBAAmB,CAACE,gBAAgB,CAAC,CAACzB,KAAK,CAAC2B,iBAAiB,CAAC,CAACT,QAAQ,GAAGA,QAAQ;MACpF;IACF,CAAC,MAAM,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACvBK,mBAAmB,CAACE,gBAAgB,CAAC,CAACzB,KAAK,CAACiC,IAAI,CAAC;QAC/CH,IAAI,EAAEd,IAAI,CAACc,IAAI,CAACC,GAAG;QACnBb,QAAQ;QACRD,KAAK,EAAED,IAAI,CAACC;MACd,CAAC,CAAC;IACJ;IAEArC,eAAe,CAAC2C,mBAAmB,CAAC;EACtC,CAAC;EAED,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B/C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMgD,sBAAsB,GAAG/B,cAAc,CAAC,CAAC;MAE/C,MAAMgC,SAAS,GAAG;QAChB7D,SAAS;QACTE,WAAW;QACXE,YAAY,EAAEwD,sBAAsB;QACpCtD;MACF,CAAC;MAED,MAAMf,KAAK,CAACuE,IAAI,CAAC,eAAelE,OAAO,QAAQ,EAAEiE,SAAS,CAAC;MAC3DrE,KAAK,CAACuE,OAAO,CAAC,kCAAkC,CAAC;;MAEjD;MACA,MAAMC,QAAQ,GAAG,MAAMzE,KAAK,CAAC0E,GAAG,CAAC,eAAerE,OAAO,QAAQ,CAAC;MAChEkB,gBAAgB,CAACkD,QAAQ,CAACE,IAAI,CAACvC,SAAS,CAAC;IAC3C,CAAC,CAAC,OAAOwC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACd7E,KAAK,CAAC2E,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,6BAA6B,CAAC;IAC7E,CAAC,SAAS;MACR1D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2D,mBAAmB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,aAAa,EAAEC,aAAa,KAAK;IACjF,IAAI;MACF,MAAMnF,KAAK,CAACoF,GAAG,CAAC,eAAe/E,OAAO,sBAAsB4E,aAAa,EAAE,EAAE;QAC3EC,aAAa;QACbC;MACF,CAAC,CAAC;MACFlF,KAAK,CAACuE,OAAO,CAAC,yBAAyB,CAAC;;MAExC;MACA,MAAMC,QAAQ,GAAG,MAAMzE,KAAK,CAAC0E,GAAG,CAAC,eAAerE,OAAO,QAAQ,CAAC;MAChEkB,gBAAgB,CAACkD,QAAQ,CAACE,IAAI,CAACvC,SAAS,CAAC;IAC3C,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACd3E,KAAK,CAAC2E,KAAK,CAAC,iCAAiC,CAAC;IAChD;EACF,CAAC;EAED,MAAMS,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,IAAI,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,UAAUpF,OAAO,EAAE;IACzDqF,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,IAAI,CAAC;IACnCrF,KAAK,CAACuE,OAAO,CAAC,qCAAqC,CAAC;EACtD,CAAC;EAED,IAAIlD,aAAa,EAAE;IACjB,oBACEnB,OAAA;MAAK0F,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B3F,OAAA;QAAK0F,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC3F,OAAA;UAAK0F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3F,OAAA;YAAA2F,QAAA,GAAI,sBAAoB,EAACvF,SAAS,CAACwF,WAAW;UAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpDhG,OAAA;YAAQ0F,SAAS,EAAC,WAAW;YAACO,OAAO,EAAE9F,OAAQ;YAAAwF,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAENhG,OAAA;UAAK0F,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3F,OAAA;YAAK0F,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3F,OAAA;cAAA2F,QAAA,GAAI,iBAAe,EAACvF,SAAS,CAACiC,WAAW;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/ChG,OAAA;cAAA2F,QAAA,GAAG,cAAY,EAACxE,aAAa,CAACb,SAAS;YAAA;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5ChG,OAAA;cAAA2F,QAAA,GAAG,gBAAc,EAACxE,aAAa,CAACX,WAAW;YAAA;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAENhG,OAAA;YAAQ0F,SAAS,EAAC,WAAW;YAACO,OAAO,EAAEf,qBAAsB;YAAAS,QAAA,EAAC;UAE9D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhG,OAAA;UAAK0F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3F,OAAA;YAAA2F,QAAA,EAAI;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACpB7E,aAAa,CAACT,YAAY,CAAC8B,GAAG,CAAC,CAACC,WAAW,EAAEf,KAAK,kBACjD1B,OAAA;YAAiB0F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC3C3F,OAAA;cAAK0F,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B3F,OAAA;gBAAA2F,QAAA,EAAKlD,WAAW,CAACd;cAAI;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3BhG,OAAA;gBAAA2F,QAAA,GAAG,WAAS,EAAClD,WAAW,CAACX,MAAM;cAAA;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpChG,OAAA;gBAAA2F,QAAA,GAAG,SACD,eAAA3F,OAAA;kBAAM0F,SAAS,EAAE,UAAUjD,WAAW,CAACsC,aAAa,EAAG;kBAAAY,QAAA,EACpDlD,WAAW,CAACsC;gBAAa;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACHvD,WAAW,CAACb,KAAK,iBAAI5B,OAAA;gBAAA2F,QAAA,GAAG,SAAO,EAAClD,WAAW,CAACb,KAAK;cAAA;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACtDvD,WAAW,CAACZ,KAAK,iBAAI7B,OAAA;gBAAA2F,QAAA,GAAG,SAAO,EAAClD,WAAW,CAACZ,KAAK;cAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAENhG,OAAA;cAAK0F,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAC7BlD,WAAW,CAACsC,aAAa,KAAK,SAAS,iBACtC/E,OAAA;gBAAK0F,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B3F,OAAA;kBACEiG,OAAO,EAAEA,CAAA,KAAMpB,mBAAmB,CAACpC,WAAW,CAACqB,GAAG,EAAE,MAAM,EAAE,MAAM,CAAE;kBACpE4B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC7B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThG,OAAA;kBACEiG,OAAO,EAAEA,CAAA,KAAMpB,mBAAmB,CAACpC,WAAW,CAACqB,GAAG,EAAE,MAAM,EAAE,MAAM,CAAE;kBACpE4B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAC7B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EACAvD,WAAW,CAACsC,aAAa,KAAK,MAAM,iBACnC/E,OAAA;gBAAK0F,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3F,OAAA;kBAAA2F,QAAA,GAAG,kBAAW,EAAClD,WAAW,CAACuC,aAAa;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7ChG,OAAA;kBAAA2F,QAAA,GAAG,WAAS,EAAC,IAAIO,IAAI,CAACzD,WAAW,CAAC0D,MAAM,CAAC,CAACC,cAAc,CAAC,CAAC;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GApCEtE,KAAK;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqCV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhG,OAAA;IAAK0F,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/B3F,OAAA;MAAK0F,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC3F,OAAA;QAAK0F,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3F,OAAA;UAAA2F,QAAA,GAAI,sBAAoB,EAACvF,SAAS,CAACwF,WAAW;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpDhG,OAAA;UAAQ0F,SAAS,EAAC,WAAW;UAACO,OAAO,EAAE9F,OAAQ;UAAAwF,QAAA,EAAC;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAENhG,OAAA;QAAK0F,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3F,OAAA;UAAK0F,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3F,OAAA;YAAA2F,QAAA,GAAI,gBAAc,EAACvF,SAAS,CAACiC,WAAW;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9ChG,OAAA;YAAK0F,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxBvF,SAAS,CAAC2B,KAAK,CAACS,GAAG,CAAC,CAACO,IAAI,EAAErB,KAAK,kBAC/B1B,OAAA;cAAiB0F,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC/B3F,OAAA;gBAAA2F,QAAA,GAAO5C,IAAI,CAACc,IAAI,CAAClC,IAAI,EAAC,IAAE,EAACoB,IAAI,CAACE,QAAQ;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9ChG,OAAA;gBAAA2F,QAAA,GAAM,GAAC,EAAC,CAAC5C,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACE,QAAQ,EAAEN,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAF/CtE,KAAK;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhG,OAAA;UAAK0F,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3F,OAAA;YAAK0F,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3F,OAAA;cAAA2F,QAAA,EAAO;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BhG,OAAA;cAAQqD,KAAK,EAAE/C,SAAU;cAAC+F,QAAQ,EAAGC,CAAC,IAAK/F,YAAY,CAAC+F,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;cAAAsC,QAAA,gBACtE3F,OAAA;gBAAQqD,KAAK,EAAC,OAAO;gBAAAsC,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChG,OAAA;gBAAQqD,KAAK,EAAC,QAAQ;gBAAAsC,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9ChG,OAAA;gBAAQqD,KAAK,EAAC,SAAS;gBAAAsC,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhG,OAAA;YAAK0F,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3F,OAAA;cAAA2F,QAAA,EAAO;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChChG,OAAA;cACEwG,IAAI,EAAC,QAAQ;cACbC,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,IAAI;cACRrD,KAAK,EAAE7C,WAAY;cACnB6F,QAAQ,EAAGC,CAAC,IAAK7F,cAAc,CAACkG,QAAQ,CAACL,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAC;YAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhG,OAAA;YAAK0F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B3F,OAAA;cAAA2F,QAAA,EAAI;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBhG,OAAA;cAAK0F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3F,OAAA;gBAAK0F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3F,OAAA;kBAAA2F,QAAA,EAAO;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnBhG,OAAA;kBACEwG,IAAI,EAAC,QAAQ;kBACbI,IAAI,EAAC,MAAM;kBACXvD,KAAK,EAAEzC,YAAY,CAACE,GAAI;kBACxBuF,QAAQ,EAAGC,CAAC,IAAKzF,eAAe,CAAC;oBAC/B,GAAGD,YAAY;oBACfE,GAAG,EAAE4B,UAAU,CAAC4D,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAC,IAAI;kBACrC,CAAC;gBAAE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNhG,OAAA;gBAAK0F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3F,OAAA;kBAAA2F,QAAA,EAAO;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnBhG,OAAA;kBACEwG,IAAI,EAAC,QAAQ;kBACbI,IAAI,EAAC,MAAM;kBACXvD,KAAK,EAAEzC,YAAY,CAACG,GAAI;kBACxBsF,QAAQ,EAAGC,CAAC,IAAKzF,eAAe,CAAC;oBAC/B,GAAGD,YAAY;oBACfG,GAAG,EAAE2B,UAAU,CAAC4D,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAC,IAAI;kBACrC,CAAC;gBAAE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNhG,OAAA;gBAAK0F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3F,OAAA;kBAAA2F,QAAA,EAAO;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3BhG,OAAA;kBACEwG,IAAI,EAAC,QAAQ;kBACbI,IAAI,EAAC,MAAM;kBACXvD,KAAK,EAAEzC,YAAY,CAACI,UAAW;kBAC/BqF,QAAQ,EAAGC,CAAC,IAAKzF,eAAe,CAAC;oBAC/B,GAAGD,YAAY;oBACfI,UAAU,EAAE0B,UAAU,CAAC4D,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAC,IAAI;kBAC5C,CAAC;gBAAE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhG,OAAA;UAAK0F,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC3F,OAAA;YAAA2F,QAAA,EAAI;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACpBtF,YAAY,CAAC8B,GAAG,CAAC,CAACC,WAAW,EAAEf,KAAK,kBACnC1B,OAAA;YAAiB0F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC3C3F,OAAA;cAAK0F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC3F,OAAA;gBACEwG,IAAI,EAAC,MAAM;gBACXK,WAAW,EAAC,MAAM;gBAClBxD,KAAK,EAAEZ,WAAW,CAACd,IAAK;gBACxB0E,QAAQ,EAAGC,CAAC,IAAKnD,uBAAuB,CAACzB,KAAK,EAAE,MAAM,EAAE4E,CAAC,CAACC,MAAM,CAAClD,KAAK;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACFhG,OAAA;gBACEwG,IAAI,EAAC,OAAO;gBACZK,WAAW,EAAC,kBAAkB;gBAC9BxD,KAAK,EAAEZ,WAAW,CAACb,KAAM;gBACzByE,QAAQ,EAAGC,CAAC,IAAKnD,uBAAuB,CAACzB,KAAK,EAAE,OAAO,EAAE4E,CAAC,CAACC,MAAM,CAAClD,KAAK;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACFhG,OAAA;gBACEwG,IAAI,EAAC,KAAK;gBACVK,WAAW,EAAC,kBAAkB;gBAC9BxD,KAAK,EAAEZ,WAAW,CAACZ,KAAM;gBACzBwE,QAAQ,EAAGC,CAAC,IAAKnD,uBAAuB,CAACzB,KAAK,EAAE,OAAO,EAAE4E,CAAC,CAACC,MAAM,CAAClD,KAAK;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAEL1F,SAAS,KAAK,QAAQ,iBACrBN,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3F,OAAA;gBAAA2F,QAAA,EAAO;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBhG,OAAA;gBACEwG,IAAI,EAAC,QAAQ;gBACbI,IAAI,EAAC,MAAM;gBACXvD,KAAK,EAAEZ,WAAW,CAACX,MAAO;gBAC1BuE,QAAQ,EAAGC,CAAC,IAAKnD,uBAAuB,CAACzB,KAAK,EAAE,QAAQ,EAAEgB,UAAU,CAAC4D,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAC,IAAI,CAAC;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,EAEA1F,SAAS,KAAK,SAAS,iBACtBN,OAAA;cAAK0F,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B3F,OAAA;gBAAA2F,QAAA,EAAI;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACrB5F,SAAS,CAAC2B,KAAK,CAACS,GAAG,CAAC,CAACO,IAAI,EAAEU,SAAS,kBACnCzD,OAAA;gBAAqB0F,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1C3F,OAAA;kBAAA2F,QAAA,GAAO5C,IAAI,CAACc,IAAI,CAAClC,IAAI,EAAC,KAAG,EAACoB,IAAI,CAACC,KAAK,EAAC,GAAC;gBAAA;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7ChG,OAAA;kBACEwG,IAAI,EAAC,QAAQ;kBACbC,GAAG,EAAC,GAAG;kBACPC,GAAG,EAAE3D,IAAI,CAACE,QAAS;kBACnB4D,WAAW,EAAC,KAAK;kBACjBR,QAAQ,EAAGC,CAAC,IAAK/C,oBAAoB,CAAC7B,KAAK,EAAE+B,SAAS,EAAEkD,QAAQ,CAACL,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAC,IAAI,CAAC;gBAAE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA,GARMvC,SAAS;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASd,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,EAEA1F,SAAS,KAAK,OAAO,iBACpBN,OAAA;cAAK0F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChC3F,OAAA;gBAAA2F,QAAA,GAAQ,WAAS,EAAC,CAAC,CAACvF,SAAS,CAACiC,WAAW,GAAGzB,YAAY,CAACE,GAAG,GAAGF,YAAY,CAACG,GAAG,GAAGH,YAAY,CAACI,UAAU,IAAIR,WAAW,EAAEmC,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3I,CACN;UAAA,GAxDOtE,KAAK;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhG,OAAA;UAAK0F,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3F,OAAA;YAAQ0F,SAAS,EAAC,mBAAmB;YAACO,OAAO,EAAE9F,OAAQ;YAAAwF,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA;YACE0F,SAAS,EAAC,iBAAiB;YAC3BO,OAAO,EAAEhC,YAAa;YACtB6C,QAAQ,EAAE7F,OAAQ;YAAA0E,QAAA,EAEjB1E,OAAO,GAAG,eAAe,GAAG;UAAmB;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3F,EAAA,CArXIJ,SAAS;AAAA8G,EAAA,GAAT9G,SAAS;AAuXf,eAAeA,SAAS;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}