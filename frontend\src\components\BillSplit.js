import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import '../styles/BillSplit.css';

const BillSplit = ({ orderId, onClose, orderData }) => {
  const [splitType, setSplitType] = useState('equal');
  const [totalPeople, setTotalPeople] = useState(2);
  const [participants, setParticipants] = useState([]);
  const [splitDetails, setSplitDetails] = useState({
    tax: 0,
    tip: 0,
    serviceFee: 0
  });
  const [loading, setLoading] = useState(false);
  const [billSplitData, setBillSplitData] = useState(null);

  useEffect(() => {
    // Initialize participants based on total people
    const newParticipants = Array.from({ length: totalPeople }, (_, index) => ({
      name: `Person ${index + 1}`,
      email: '',
      phone: '',
      amount: 0,
      items: []
    }));
    setParticipants(newParticipants);
  }, [totalPeople]);

  useEffect(() => {
    // Check if bill split is already enabled
    if (orderData?.billSplit?.isEnabled) {
      setBillSplitData(orderData.billSplit);
    }
  }, [orderData]);

  const calculateSplit = () => {
    const baseAmount = orderData.totalAmount;
    const totalWithExtras = baseAmount + splitDetails.tax + splitDetails.tip + splitDetails.serviceFee;

    if (splitType === 'equal') {
      const perPersonAmount = totalWithExtras / totalPeople;
      return participants.map(participant => ({
        ...participant,
        amount: parseFloat(perPersonAmount.toFixed(2))
      }));
    } else if (splitType === 'custom') {
      return participants;
    } else if (splitType === 'by-item') {
      return participants.map(participant => {
        const itemTotal = participant.items.reduce((sum, item) => {
          return sum + (item.price * item.quantity);
        }, 0);
        const participantShare = (itemTotal / baseAmount) * totalWithExtras;
        return {
          ...participant,
          amount: parseFloat(participantShare.toFixed(2))
        };
      });
    }
    return participants;
  };

  const handleParticipantChange = (index, field, value) => {
    const updatedParticipants = [...participants];
    updatedParticipants[index] = {
      ...updatedParticipants[index],
      [field]: value
    };
    setParticipants(updatedParticipants);
  };

  const handleItemAssignment = (participantIndex, itemIndex, quantity) => {
    const updatedParticipants = [...participants];
    const item = orderData.items[itemIndex];
    
    const existingItemIndex = updatedParticipants[participantIndex].items.findIndex(
      i => i.dish === item.dish._id
    );

    if (existingItemIndex >= 0) {
      if (quantity === 0) {
        updatedParticipants[participantIndex].items.splice(existingItemIndex, 1);
      } else {
        updatedParticipants[participantIndex].items[existingItemIndex].quantity = quantity;
      }
    } else if (quantity > 0) {
      updatedParticipants[participantIndex].items.push({
        dish: item.dish._id,
        quantity,
        price: item.price
      });
    }

    setParticipants(updatedParticipants);
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const calculatedParticipants = calculateSplit();
      
      const splitData = {
        splitType,
        totalPeople,
        participants: calculatedParticipants,
        splitDetails
      };

      await axios.post(`/api/orders/${orderId}/split`, splitData);
      toast.success('Bill split enabled successfully!');
      
      // Refresh bill split data
      const response = await axios.get(`/api/orders/${orderId}/split`);
      setBillSplitData(response.data.billSplit);
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to enable bill split');
    } finally {
      setLoading(false);
    }
  };

  const updatePaymentStatus = async (participantId, paymentStatus, paymentMethod) => {
    try {
      await axios.put(`/api/orders/${orderId}/split/participant/${participantId}`, {
        paymentStatus,
        paymentMethod
      });
      toast.success('Payment status updated!');
      
      // Refresh bill split data
      const response = await axios.get(`/api/orders/${orderId}/split`);
      setBillSplitData(response.data.billSplit);
    } catch (error) {
      toast.error('Failed to update payment status');
    }
  };

  const generateShareableLink = () => {
    const link = `${window.location.origin}/split/${orderId}`;
    navigator.clipboard.writeText(link);
    toast.success('Shareable link copied to clipboard!');
  };

  if (billSplitData) {
    return (
      <div className="bill-split-modal">
        <div className="bill-split-content">
          <div className="bill-split-header">
            <h2>Bill Split - Order #{orderData.orderNumber}</h2>
            <button className="close-btn" onClick={onClose}>×</button>
          </div>

          <div className="split-summary">
            <div className="total-info">
              <h3>Total Amount: ${orderData.totalAmount}</h3>
              <p>Split Type: {billSplitData.splitType}</p>
              <p>Total People: {billSplitData.totalPeople}</p>
            </div>
            
            <button className="share-btn" onClick={generateShareableLink}>
              📋 Copy Shareable Link
            </button>
          </div>

          <div className="participants-list">
            <h3>Participants</h3>
            {billSplitData.participants.map((participant, index) => (
              <div key={index} className="participant-card">
                <div className="participant-info">
                  <h4>{participant.name}</h4>
                  <p>Amount: ${participant.amount}</p>
                  <p>Status: 
                    <span className={`status ${participant.paymentStatus}`}>
                      {participant.paymentStatus}
                    </span>
                  </p>
                  {participant.email && <p>Email: {participant.email}</p>}
                  {participant.phone && <p>Phone: {participant.phone}</p>}
                </div>
                
                <div className="payment-actions">
                  {participant.paymentStatus === 'pending' && (
                    <div className="payment-methods">
                      <button 
                        onClick={() => updatePaymentStatus(participant._id, 'paid', 'cash')}
                        className="payment-btn cash"
                      >
                        Mark as Paid (Cash)
                      </button>
                      <button 
                        onClick={() => updatePaymentStatus(participant._id, 'paid', 'card')}
                        className="payment-btn card"
                      >
                        Mark as Paid (Card)
                      </button>
                    </div>
                  )}
                  {participant.paymentStatus === 'paid' && (
                    <div className="paid-info">
                      <p>✅ Paid via {participant.paymentMethod}</p>
                      <p>Paid at: {new Date(participant.paidAt).toLocaleString()}</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bill-split-modal">
      <div className="bill-split-content">
        <div className="bill-split-header">
          <h2>Split Bill - Order #{orderData.orderNumber}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="split-setup">
          <div className="order-summary">
            <h3>Order Total: ${orderData.totalAmount}</h3>
            <div className="items-list">
              {orderData.items.map((item, index) => (
                <div key={index} className="item">
                  <span>{item.dish.name} x{item.quantity}</span>
                  <span>${(item.price * item.quantity).toFixed(2)}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="split-options">
            <div className="form-group">
              <label>Split Type:</label>
              <select value={splitType} onChange={(e) => setSplitType(e.target.value)}>
                <option value="equal">Equal Split</option>
                <option value="custom">Custom Amounts</option>
                <option value="by-item">By Items</option>
              </select>
            </div>

            <div className="form-group">
              <label>Number of People:</label>
              <input
                type="number"
                min="2"
                max="20"
                value={totalPeople}
                onChange={(e) => setTotalPeople(parseInt(e.target.value))}
              />
            </div>

            <div className="additional-costs">
              <h4>Additional Costs</h4>
              <div className="cost-inputs">
                <div className="form-group">
                  <label>Tax:</label>
                  <input
                    type="number"
                    step="0.01"
                    value={splitDetails.tax}
                    onChange={(e) => setSplitDetails({
                      ...splitDetails,
                      tax: parseFloat(e.target.value) || 0
                    })}
                  />
                </div>
                <div className="form-group">
                  <label>Tip:</label>
                  <input
                    type="number"
                    step="0.01"
                    value={splitDetails.tip}
                    onChange={(e) => setSplitDetails({
                      ...splitDetails,
                      tip: parseFloat(e.target.value) || 0
                    })}
                  />
                </div>
                <div className="form-group">
                  <label>Service Fee:</label>
                  <input
                    type="number"
                    step="0.01"
                    value={splitDetails.serviceFee}
                    onChange={(e) => setSplitDetails({
                      ...splitDetails,
                      serviceFee: parseFloat(e.target.value) || 0
                    })}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="participants-setup">
            <h3>Participants</h3>
            {participants.map((participant, index) => (
              <div key={index} className="participant-form">
                <div className="participant-basic">
                  <input
                    type="text"
                    placeholder="Name"
                    value={participant.name}
                    onChange={(e) => handleParticipantChange(index, 'name', e.target.value)}
                  />
                  <input
                    type="email"
                    placeholder="Email (optional)"
                    value={participant.email}
                    onChange={(e) => handleParticipantChange(index, 'email', e.target.value)}
                  />
                  <input
                    type="tel"
                    placeholder="Phone (optional)"
                    value={participant.phone}
                    onChange={(e) => handleParticipantChange(index, 'phone', e.target.value)}
                  />
                </div>

                {splitType === 'custom' && (
                  <div className="custom-amount">
                    <label>Amount:</label>
                    <input
                      type="number"
                      step="0.01"
                      value={participant.amount}
                      onChange={(e) => handleParticipantChange(index, 'amount', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                )}

                {splitType === 'by-item' && (
                  <div className="item-assignment">
                    <h4>Assign Items:</h4>
                    {orderData.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="item-assign">
                        <span>{item.dish.name} (${item.price})</span>
                        <input
                          type="number"
                          min="0"
                          max={item.quantity}
                          placeholder="Qty"
                          onChange={(e) => handleItemAssignment(index, itemIndex, parseInt(e.target.value) || 0)}
                        />
                      </div>
                    ))}
                  </div>
                )}

                {splitType === 'equal' && (
                  <div className="calculated-amount">
                    <strong>Amount: ${((orderData.totalAmount + splitDetails.tax + splitDetails.tip + splitDetails.serviceFee) / totalPeople).toFixed(2)}</strong>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="split-actions">
            <button className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button 
              className="btn btn-primary" 
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? 'Setting up...' : 'Enable Bill Split'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillSplit;
