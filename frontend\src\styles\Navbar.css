.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo {
  font-size: 24px;
  font-weight: bold;
  color: #e74c3c;
  text-decoration: none;
  transition: color 0.3s ease;
}

.nav-logo:hover {
  color: #c0392b;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 30px;
  list-style: none;
}

.nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.nav-link:hover {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

.admin-link {
  background-color: #e74c3c;
  color: white !important;
}

.admin-link:hover {
  background-color: #c0392b;
  color: white !important;
}

.logout-btn {
  background-color: #95a5a6;
  color: white;
}

.logout-btn:hover {
  background-color: #7f8c8d;
  color: white;
}

.cart-btn {
  position: relative;
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cart-btn:hover {
  background-color: #c0392b;
  transform: translateY(-2px);
}

.cart-count {
  background-color: #f39c12;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: bold;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: -5px;
  right: -5px;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 5px;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: #333;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .nav-container {
    height: 60px;
    padding: 0 15px;
  }

  .nav-logo {
    font-size: 20px;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 60px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 20px 0;
    gap: 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-link {
    padding: 15px 20px;
    display: block;
    width: 100%;
    margin: 0;
  }

  .cart-btn {
    margin: 10px 20px;
    justify-content: center;
  }

  /* Hamburger Animation */
  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .nav-toggle.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 10px;
  }

  .nav-logo {
    font-size: 18px;
  }

  .nav-link {
    padding: 12px 15px;
    font-size: 14px;
  }
}
