{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Restaurant App\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: false,\n  loading: true,\n  error: null\n};\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'USER_LOADED':\n      return {\n        ...state,\n        isAuthenticated: true,\n        loading: false,\n        user: action.payload,\n        error: null\n      };\n    case 'LOGIN_SUCCESS':\n    case 'REGISTER_SUCCESS':\n      localStorage.setItem('token', action.payload.token);\n      return {\n        ...state,\n        ...action.payload,\n        isAuthenticated: true,\n        loading: false,\n        error: null\n      };\n    case 'AUTH_ERROR':\n    case 'LOGIN_FAIL':\n    case 'REGISTER_FAIL':\n    case 'LOGOUT':\n      localStorage.removeItem('token');\n      return {\n        ...state,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        user: null,\n        error: action.payload\n      };\n    case 'CLEAR_ERRORS':\n      return {\n        ...state,\n        error: null\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set auth token in axios headers\n  const setAuthToken = token => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Load user\n  const loadUser = async () => {\n    if (localStorage.token) {\n      setAuthToken(localStorage.token);\n    }\n    try {\n      const res = await axios.get('/api/auth/profile');\n      dispatch({\n        type: 'USER_LOADED',\n        payload: res.data\n      });\n    } catch (err) {\n      dispatch({\n        type: 'AUTH_ERROR'\n      });\n    }\n  };\n\n  // Register user\n  const register = async formData => {\n    try {\n      dispatch({\n        type: 'SET_LOADING',\n        payload: true\n      });\n      const res = await axios.post('/api/auth/register', formData);\n      dispatch({\n        type: 'REGISTER_SUCCESS',\n        payload: res.data\n      });\n      setAuthToken(res.data.token);\n      toast.success('Registration successful!');\n      return {\n        success: true\n      };\n    } catch (err) {\n      var _err$response, _err$response$data;\n      const message = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Registration failed';\n      dispatch({\n        type: 'REGISTER_FAIL',\n        payload: message\n      });\n      toast.error(message);\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Login user\n  const login = async formData => {\n    try {\n      dispatch({\n        type: 'SET_LOADING',\n        payload: true\n      });\n      const res = await axios.post('/api/auth/login', formData);\n      dispatch({\n        type: 'LOGIN_SUCCESS',\n        payload: res.data\n      });\n      setAuthToken(res.data.token);\n      toast.success('Login successful!');\n      return {\n        success: true\n      };\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      const message = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Login failed';\n      dispatch({\n        type: 'LOGIN_FAIL',\n        payload: message\n      });\n      toast.error(message);\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Logout\n  const logout = () => {\n    dispatch({\n      type: 'LOGOUT'\n    });\n    setAuthToken(null);\n    toast.info('Logged out successfully');\n  };\n\n  // Update profile\n  const updateProfile = async formData => {\n    try {\n      const res = await axios.put('/api/auth/profile', formData);\n      dispatch({\n        type: 'USER_LOADED',\n        payload: res.data\n      });\n      toast.success('Profile updated successfully!');\n      return {\n        success: true\n      };\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      const message = ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Profile update failed';\n      toast.error(message);\n      return {\n        success: false,\n        message\n      };\n    }\n  };\n\n  // Clear errors\n  const clearErrors = () => dispatch({\n    type: 'CLEAR_ERRORS'\n  });\n  useEffect(() => {\n    // Set base URL for axios\n    axios.defaults.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    loadUser();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      ...state,\n      register,\n      login,\n      logout,\n      updateProfile,\n      clearErrors,\n      loadUser\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "axios", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "initialState", "user", "token", "localStorage", "getItem", "isAuthenticated", "loading", "error", "authReducer", "state", "action", "type", "payload", "setItem", "removeItem", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "setAuthToken", "defaults", "headers", "common", "loadUser", "res", "get", "data", "err", "register", "formData", "post", "success", "_err$response", "_err$response$data", "message", "response", "login", "_err$response2", "_err$response2$data", "logout", "info", "updateProfile", "put", "_err$response3", "_err$response3$data", "clearErrors", "baseURL", "process", "env", "REACT_APP_API_URL", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "undefined", "Error", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\n\nconst AuthContext = createContext();\n\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isAuthenticated: false,\n  loading: true,\n  error: null\n};\n\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'USER_LOADED':\n      return {\n        ...state,\n        isAuthenticated: true,\n        loading: false,\n        user: action.payload,\n        error: null\n      };\n    case 'LOGIN_SUCCESS':\n    case 'REGISTER_SUCCESS':\n      localStorage.setItem('token', action.payload.token);\n      return {\n        ...state,\n        ...action.payload,\n        isAuthenticated: true,\n        loading: false,\n        error: null\n      };\n    case 'AUTH_ERROR':\n    case 'LOGIN_FAIL':\n    case 'REGISTER_FAIL':\n    case 'LOGOUT':\n      localStorage.removeItem('token');\n      return {\n        ...state,\n        token: null,\n        isAuthenticated: false,\n        loading: false,\n        user: null,\n        error: action.payload\n      };\n    case 'CLEAR_ERRORS':\n      return {\n        ...state,\n        error: null\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set auth token in axios headers\n  const setAuthToken = (token) => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  };\n\n  // Load user\n  const loadUser = async () => {\n    if (localStorage.token) {\n      setAuthToken(localStorage.token);\n    }\n\n    try {\n      const res = await axios.get('/api/auth/profile');\n      dispatch({\n        type: 'USER_LOADED',\n        payload: res.data\n      });\n    } catch (err) {\n      dispatch({ type: 'AUTH_ERROR' });\n    }\n  };\n\n  // Register user\n  const register = async (formData) => {\n    try {\n      dispatch({ type: 'SET_LOADING', payload: true });\n      \n      const res = await axios.post('/api/auth/register', formData);\n      \n      dispatch({\n        type: 'REGISTER_SUCCESS',\n        payload: res.data\n      });\n      \n      setAuthToken(res.data.token);\n      toast.success('Registration successful!');\n      return { success: true };\n    } catch (err) {\n      const message = err.response?.data?.message || 'Registration failed';\n      dispatch({\n        type: 'REGISTER_FAIL',\n        payload: message\n      });\n      toast.error(message);\n      return { success: false, message };\n    }\n  };\n\n  // Login user\n  const login = async (formData) => {\n    try {\n      dispatch({ type: 'SET_LOADING', payload: true });\n      \n      const res = await axios.post('/api/auth/login', formData);\n      \n      dispatch({\n        type: 'LOGIN_SUCCESS',\n        payload: res.data\n      });\n      \n      setAuthToken(res.data.token);\n      toast.success('Login successful!');\n      return { success: true };\n    } catch (err) {\n      const message = err.response?.data?.message || 'Login failed';\n      dispatch({\n        type: 'LOGIN_FAIL',\n        payload: message\n      });\n      toast.error(message);\n      return { success: false, message };\n    }\n  };\n\n  // Logout\n  const logout = () => {\n    dispatch({ type: 'LOGOUT' });\n    setAuthToken(null);\n    toast.info('Logged out successfully');\n  };\n\n  // Update profile\n  const updateProfile = async (formData) => {\n    try {\n      const res = await axios.put('/api/auth/profile', formData);\n      \n      dispatch({\n        type: 'USER_LOADED',\n        payload: res.data\n      });\n      \n      toast.success('Profile updated successfully!');\n      return { success: true };\n    } catch (err) {\n      const message = err.response?.data?.message || 'Profile update failed';\n      toast.error(message);\n      return { success: false, message };\n    }\n  };\n\n  // Clear errors\n  const clearErrors = () => dispatch({ type: 'CLEAR_ERRORS' });\n\n  useEffect(() => {\n    // Set base URL for axios\n    axios.defaults.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    \n    loadUser();\n  }, []);\n\n  return (\n    <AuthContext.Provider\n      value={{\n        ...state,\n        register,\n        login,\n        logout,\n        updateProfile,\n        clearErrors,\n        loadUser\n      }}\n    >\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,MAAMS,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCC,eAAe,EAAE,KAAK;EACtBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAO;QACL,GAAGF,KAAK;QACRJ,eAAe,EAAE,IAAI;QACrBC,OAAO,EAAE,KAAK;QACdL,IAAI,EAAES,MAAM,CAACE,OAAO;QACpBL,KAAK,EAAE;MACT,CAAC;IACH,KAAK,eAAe;IACpB,KAAK,kBAAkB;MACrBJ,YAAY,CAACU,OAAO,CAAC,OAAO,EAAEH,MAAM,CAACE,OAAO,CAACV,KAAK,CAAC;MACnD,OAAO;QACL,GAAGO,KAAK;QACR,GAAGC,MAAM,CAACE,OAAO;QACjBP,eAAe,EAAE,IAAI;QACrBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,YAAY;IACjB,KAAK,YAAY;IACjB,KAAK,eAAe;IACpB,KAAK,QAAQ;MACXJ,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;MAChC,OAAO;QACL,GAAGL,KAAK;QACRP,KAAK,EAAE,IAAI;QACXG,eAAe,EAAE,KAAK;QACtBC,OAAO,EAAE,KAAK;QACdL,IAAI,EAAE,IAAI;QACVM,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGH,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRH,OAAO,EAAEI,MAAM,CAACE;MAClB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMM,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACR,KAAK,EAAES,QAAQ,CAAC,GAAGzB,UAAU,CAACe,WAAW,EAAER,YAAY,CAAC;;EAE/D;EACA,MAAMmB,YAAY,GAAIjB,KAAK,IAAK;IAC9B,IAAIA,KAAK,EAAE;MACTP,KAAK,CAACyB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUpB,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOP,KAAK,CAACyB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAIpB,YAAY,CAACD,KAAK,EAAE;MACtBiB,YAAY,CAAChB,YAAY,CAACD,KAAK,CAAC;IAClC;IAEA,IAAI;MACF,MAAMsB,GAAG,GAAG,MAAM7B,KAAK,CAAC8B,GAAG,CAAC,mBAAmB,CAAC;MAChDP,QAAQ,CAAC;QACPP,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAEY,GAAG,CAACE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZT,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAMiB,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACFX,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAEhD,MAAMY,GAAG,GAAG,MAAM7B,KAAK,CAACmC,IAAI,CAAC,oBAAoB,EAAED,QAAQ,CAAC;MAE5DX,QAAQ,CAAC;QACPP,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAEY,GAAG,CAACE;MACf,CAAC,CAAC;MAEFP,YAAY,CAACK,GAAG,CAACE,IAAI,CAACxB,KAAK,CAAC;MAC5BN,KAAK,CAACmC,OAAO,CAAC,0BAA0B,CAAC;MACzC,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOJ,GAAG,EAAE;MAAA,IAAAK,aAAA,EAAAC,kBAAA;MACZ,MAAMC,OAAO,GAAG,EAAAF,aAAA,GAAAL,GAAG,CAACQ,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcN,IAAI,cAAAO,kBAAA,uBAAlBA,kBAAA,CAAoBC,OAAO,KAAI,qBAAqB;MACpEhB,QAAQ,CAAC;QACPP,IAAI,EAAE,eAAe;QACrBC,OAAO,EAAEsB;MACX,CAAC,CAAC;MACFtC,KAAK,CAACW,KAAK,CAAC2B,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAME,KAAK,GAAG,MAAOP,QAAQ,IAAK;IAChC,IAAI;MACFX,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAEhD,MAAMY,GAAG,GAAG,MAAM7B,KAAK,CAACmC,IAAI,CAAC,iBAAiB,EAAED,QAAQ,CAAC;MAEzDX,QAAQ,CAAC;QACPP,IAAI,EAAE,eAAe;QACrBC,OAAO,EAAEY,GAAG,CAACE;MACf,CAAC,CAAC;MAEFP,YAAY,CAACK,GAAG,CAACE,IAAI,CAACxB,KAAK,CAAC;MAC5BN,KAAK,CAACmC,OAAO,CAAC,mBAAmB,CAAC;MAClC,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOJ,GAAG,EAAE;MAAA,IAAAU,cAAA,EAAAC,mBAAA;MACZ,MAAMJ,OAAO,GAAG,EAAAG,cAAA,GAAAV,GAAG,CAACQ,QAAQ,cAAAE,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcX,IAAI,cAAAY,mBAAA,uBAAlBA,mBAAA,CAAoBJ,OAAO,KAAI,cAAc;MAC7DhB,QAAQ,CAAC;QACPP,IAAI,EAAE,YAAY;QAClBC,OAAO,EAAEsB;MACX,CAAC,CAAC;MACFtC,KAAK,CAACW,KAAK,CAAC2B,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMK,MAAM,GAAGA,CAAA,KAAM;IACnBrB,QAAQ,CAAC;MAAEP,IAAI,EAAE;IAAS,CAAC,CAAC;IAC5BQ,YAAY,CAAC,IAAI,CAAC;IAClBvB,KAAK,CAAC4C,IAAI,CAAC,yBAAyB,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAOZ,QAAQ,IAAK;IACxC,IAAI;MACF,MAAML,GAAG,GAAG,MAAM7B,KAAK,CAAC+C,GAAG,CAAC,mBAAmB,EAAEb,QAAQ,CAAC;MAE1DX,QAAQ,CAAC;QACPP,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAEY,GAAG,CAACE;MACf,CAAC,CAAC;MAEF9B,KAAK,CAACmC,OAAO,CAAC,+BAA+B,CAAC;MAC9C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOJ,GAAG,EAAE;MAAA,IAAAgB,cAAA,EAAAC,mBAAA;MACZ,MAAMV,OAAO,GAAG,EAAAS,cAAA,GAAAhB,GAAG,CAACQ,QAAQ,cAAAQ,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjB,IAAI,cAAAkB,mBAAA,uBAAlBA,mBAAA,CAAoBV,OAAO,KAAI,uBAAuB;MACtEtC,KAAK,CAACW,KAAK,CAAC2B,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMW,WAAW,GAAGA,CAAA,KAAM3B,QAAQ,CAAC;IAAEP,IAAI,EAAE;EAAe,CAAC,CAAC;EAE5DjB,SAAS,CAAC,MAAM;IACd;IACAC,KAAK,CAACyB,QAAQ,CAAC0B,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;IAEjF1B,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEzB,OAAA,CAACC,WAAW,CAACmD,QAAQ;IACnBC,KAAK,EAAE;MACL,GAAG1C,KAAK;MACRmB,QAAQ;MACRQ,KAAK;MACLG,MAAM;MACNE,aAAa;MACbI,WAAW;MACXtB;IACF,CAAE;IAAAP,QAAA,EAEDA;EAAQ;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACtC,EAAA,CApIWF,YAAY;AAAAyC,EAAA,GAAZzC,YAAY;AAsIzB,OAAO,MAAM0C,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGnE,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI4D,OAAO,KAAKC,SAAS,EAAE;IACzB,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOF,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAM,YAAA,CAAAN,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}