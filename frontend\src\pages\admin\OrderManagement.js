import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

const OrderManagement = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: 'all',
    orderType: 'all'
  });
  const [selectedOrder, setSelectedOrder] = useState(null);

  useEffect(() => {
    fetchOrders();
  }, [filters]);

  const fetchOrders = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.status !== 'all') params.append('status', filters.status);
      if (filters.orderType !== 'all') params.append('orderType', filters.orderType);
      
      const response = await axios.get(`/api/orders/admin/all?${params}`);
      setOrders(response.data.orders);
    } catch (error) {
      toast.error('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      await axios.put(`/api/orders/${orderId}/status`, { status: newStatus });
      toast.success('Order status updated successfully!');
      fetchOrders();
      if (selectedOrder && selectedOrder._id === orderId) {
        setSelectedOrder({ ...selectedOrder, status: newStatus });
      }
    } catch (error) {
      toast.error('Failed to update order status');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: '#f39c12',
      confirmed: '#3498db',
      preparing: '#e67e22',
      ready: '#2ecc71',
      delivered: '#27ae60',
      cancelled: '#e74c3c'
    };
    return colors[status] || '#95a5a6';
  };

  const getNextStatus = (currentStatus) => {
    const statusFlow = {
      pending: 'confirmed',
      confirmed: 'preparing',
      preparing: 'ready',
      ready: 'delivered'
    };
    return statusFlow[currentStatus];
  };

  if (loading) {
    return <div className="loading">Loading orders...</div>;
  }

  return (
    <div className="order-management">
      <div className="container">
        <div className="page-header">
          <h1>Order Management</h1>
          <div className="filters">
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="preparing">Preparing</option>
              <option value="ready">Ready</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <select
              value={filters.orderType}
              onChange={(e) => setFilters({ ...filters, orderType: e.target.value })}
            >
              <option value="all">All Types</option>
              <option value="delivery">Delivery</option>
              <option value="pickup">Pickup</option>
              <option value="dine-in">Dine In</option>
            </select>
          </div>
        </div>

        <div className="orders-layout">
          <div className="orders-list">
            {orders.length === 0 ? (
              <div className="no-orders">No orders found</div>
            ) : (
              orders.map((order) => (
                <div 
                  key={order._id} 
                  className={`order-item ${selectedOrder?._id === order._id ? 'selected' : ''}`}
                  onClick={() => setSelectedOrder(order)}
                >
                  <div className="order-header">
                    <h3>#{order.orderNumber}</h3>
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(order.status) }}
                    >
                      {order.status}
                    </span>
                  </div>
                  <div className="order-info">
                    <p><strong>Customer:</strong> {order.customer?.name}</p>
                    <p><strong>Type:</strong> {order.orderType}</p>
                    <p><strong>Total:</strong> ${order.totalAmount}</p>
                    <p><strong>Date:</strong> {new Date(order.createdAt).toLocaleString()}</p>
                  </div>
                  <div className="order-actions">
                    {getNextStatus(order.status) && (
                      <button
                        className="btn btn-primary btn-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          updateOrderStatus(order._id, getNextStatus(order.status));
                        }}
                      >
                        Mark as {getNextStatus(order.status)}
                      </button>
                    )}
                    {order.status === 'pending' && (
                      <button
                        className="btn btn-danger btn-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          updateOrderStatus(order._id, 'cancelled');
                        }}
                      >
                        Cancel
                      </button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {selectedOrder && (
            <div className="order-details">
              <div className="order-details-header">
                <h2>Order Details</h2>
                <button 
                  className="close-btn"
                  onClick={() => setSelectedOrder(null)}
                >
                  ×
                </button>
              </div>

              <div className="order-details-content">
                <div className="detail-section">
                  <h3>Order Information</h3>
                  <p><strong>Order Number:</strong> {selectedOrder.orderNumber}</p>
                  <p><strong>Status:</strong> 
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(selectedOrder.status) }}
                    >
                      {selectedOrder.status}
                    </span>
                  </p>
                  <p><strong>Type:</strong> {selectedOrder.orderType}</p>
                  <p><strong>Payment Method:</strong> {selectedOrder.paymentMethod}</p>
                  <p><strong>Created:</strong> {new Date(selectedOrder.createdAt).toLocaleString()}</p>
                </div>

                <div className="detail-section">
                  <h3>Customer Information</h3>
                  <p><strong>Name:</strong> {selectedOrder.customer?.name}</p>
                  <p><strong>Email:</strong> {selectedOrder.customer?.email}</p>
                  <p><strong>Phone:</strong> {selectedOrder.customer?.phone}</p>
                </div>

                {selectedOrder.deliveryAddress && (
                  <div className="detail-section">
                    <h3>Delivery Address</h3>
                    <p>{selectedOrder.deliveryAddress.street}</p>
                    <p>{selectedOrder.deliveryAddress.city}, {selectedOrder.deliveryAddress.state} {selectedOrder.deliveryAddress.zipCode}</p>
                    <p><strong>Phone:</strong> {selectedOrder.deliveryAddress.phone}</p>
                  </div>
                )}

                <div className="detail-section">
                  <h3>Order Items</h3>
                  <div className="order-items">
                    {selectedOrder.items.map((item, index) => (
                      <div key={index} className="order-item-detail">
                        <img 
                          src={`http://localhost:5000/uploads/${item.dish.image}`}
                          alt={item.dish.name}
                          className="item-image"
                        />
                        <div className="item-info">
                          <h4>{item.dish.name}</h4>
                          <p>Quantity: {item.quantity}</p>
                          <p>Price: ${item.price} each</p>
                          <p>Subtotal: ${(item.price * item.quantity).toFixed(2)}</p>
                          {item.specialInstructions && (
                            <p><strong>Special Instructions:</strong> {item.specialInstructions}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="order-total">
                    <h3>Total: ${selectedOrder.totalAmount}</h3>
                  </div>
                </div>

                {selectedOrder.notes && (
                  <div className="detail-section">
                    <h3>Notes</h3>
                    <p>{selectedOrder.notes}</p>
                  </div>
                )}

                <div className="detail-actions">
                  <h3>Update Status</h3>
                  <div className="status-buttons">
                    {['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'].map((status) => (
                      <button
                        key={status}
                        className={`btn ${selectedOrder.status === status ? 'btn-primary' : 'btn-secondary'}`}
                        onClick={() => updateOrderStatus(selectedOrder._id, status)}
                        disabled={selectedOrder.status === status}
                      >
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderManagement;
