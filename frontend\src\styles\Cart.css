.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  justify-content: flex-end;
}

.cart-sidebar {
  width: 450px;
  max-width: 90vw;
  background: white;
  height: 100vh;
  overflow-y: auto;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.cart-header h2 {
  color: #2c3e50;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e9ecef;
  color: #2c3e50;
}

.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-cart p {
  color: #7f8c8d;
  margin-bottom: 20px;
  font-size: 18px;
}

.continue-shopping-btn {
  padding: 12px 24px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.continue-shopping-btn:hover {
  background: #c0392b;
}

.cart-items {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
}

.cart-item-details {
  flex: 1;
}

.cart-item-details h4 {
  color: #2c3e50;
  margin: 0 0 5px 0;
  font-size: 16px;
}

.cart-item-price {
  color: #e74c3c;
  font-weight: 600;
  margin-bottom: 5px;
}

.special-instructions {
  font-size: 14px;
  color: #7f8c8d;
  font-style: italic;
}

.cart-item-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 5px;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: none;
  background: #e74c3c;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.quantity-btn:hover {
  background: #c0392b;
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

.remove-btn {
  background: none;
  border: none;
  color: #e74c3c;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
}

.remove-btn:hover {
  color: #c0392b;
}

.order-options {
  padding: 20px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
}

.order-options h3 {
  color: #2c3e50;
  margin: 0 0 15px 0;
  font-size: 18px;
}

.order-type-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.order-type-options label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.order-type-options label:hover {
  background: #e9ecef;
}

.order-type-options input[type="radio"] {
  margin: 0;
}

.delivery-address {
  margin-top: 15px;
}

.delivery-address h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 16px;
}

.delivery-address input {
  width: 100%;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 6px;
  margin-bottom: 10px;
  font-size: 14px;
}

.delivery-address input:focus {
  outline: none;
  border-color: #e74c3c;
}

.cart-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  background: white;
}

.cart-total {
  text-align: center;
  margin-bottom: 20px;
}

.cart-total h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 20px;
}

.cart-actions {
  display: flex;
  gap: 10px;
}

.clear-cart-btn,
.checkout-btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.clear-cart-btn {
  background: #95a5a6;
  color: white;
}

.clear-cart-btn:hover:not(:disabled) {
  background: #7f8c8d;
}

.checkout-btn {
  background: #27ae60;
  color: white;
}

.checkout-btn:hover:not(:disabled) {
  background: #229954;
}

.clear-cart-btn:disabled,
.checkout-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cart-sidebar {
    width: 100vw;
  }

  .cart-item {
    flex-direction: column;
    gap: 10px;
  }

  .cart-item-image {
    width: 60px;
    height: 60px;
  }

  .cart-item-controls {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .order-type-options {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .cart-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .cart-header,
  .cart-items,
  .order-options,
  .cart-footer {
    padding: 15px;
  }

  .cart-item-details h4 {
    font-size: 14px;
  }

  .order-type-options {
    flex-direction: column;
  }
}
