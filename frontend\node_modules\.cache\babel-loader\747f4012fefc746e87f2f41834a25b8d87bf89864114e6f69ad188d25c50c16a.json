{"ast": null, "code": "var _jsxFileName = \"D:\\\\D Drive\\\\Projects\\\\Restaurant App\\\\frontend\\\\src\\\\context\\\\CartContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartContext = /*#__PURE__*/createContext();\nconst initialState = {\n  items: JSON.parse(localStorage.getItem('cartItems')) || [],\n  totalAmount: 0,\n  totalItems: 0\n};\nconst cartReducer = (state, action) => {\n  switch (action.type) {\n    case 'ADD_TO_CART':\n      {\n        const existingItem = state.items.find(item => item.dish._id === action.payload.dish._id);\n        if (existingItem) {\n          const updatedItems = state.items.map(item => item.dish._id === action.payload.dish._id ? {\n            ...item,\n            quantity: item.quantity + action.payload.quantity\n          } : item);\n          return {\n            ...state,\n            items: updatedItems\n          };\n        } else {\n          return {\n            ...state,\n            items: [...state.items, action.payload]\n          };\n        }\n      }\n    case 'REMOVE_FROM_CART':\n      {\n        const updatedItems = state.items.filter(item => item.dish._id !== action.payload);\n        return {\n          ...state,\n          items: updatedItems\n        };\n      }\n    case 'UPDATE_QUANTITY':\n      {\n        const updatedItems = state.items.map(item => item.dish._id === action.payload.dishId ? {\n          ...item,\n          quantity: action.payload.quantity\n        } : item).filter(item => item.quantity > 0);\n        return {\n          ...state,\n          items: updatedItems\n        };\n      }\n    case 'CLEAR_CART':\n      return {\n        ...state,\n        items: []\n      };\n    case 'CALCULATE_TOTALS':\n      {\n        const totalAmount = state.items.reduce((total, item) => total + item.dish.price * item.quantity, 0);\n        const totalItems = state.items.reduce((total, item) => total + item.quantity, 0);\n        return {\n          ...state,\n          totalAmount: parseFloat(totalAmount.toFixed(2)),\n          totalItems\n        };\n      }\n    default:\n      return state;\n  }\n};\nexport const CartProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(cartReducer, initialState);\n\n  // Add item to cart\n  const addToCart = (dish, quantity = 1, specialInstructions = '') => {\n    dispatch({\n      type: 'ADD_TO_CART',\n      payload: {\n        dish,\n        quantity,\n        specialInstructions\n      }\n    });\n    toast.success(`${dish.name} added to cart!`);\n  };\n\n  // Remove item from cart\n  const removeFromCart = dishId => {\n    dispatch({\n      type: 'REMOVE_FROM_CART',\n      payload: dishId\n    });\n    toast.info('Item removed from cart');\n  };\n\n  // Update item quantity\n  const updateQuantity = (dishId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(dishId);\n      return;\n    }\n    dispatch({\n      type: 'UPDATE_QUANTITY',\n      payload: {\n        dishId,\n        quantity\n      }\n    });\n  };\n\n  // Clear entire cart\n  const clearCart = () => {\n    dispatch({\n      type: 'CLEAR_CART'\n    });\n    toast.info('Cart cleared');\n  };\n\n  // Get item quantity\n  const getItemQuantity = dishId => {\n    const item = state.items.find(item => item.dish._id === dishId);\n    return item ? item.quantity : 0;\n  };\n\n  // Check if item is in cart\n  const isInCart = dishId => {\n    return state.items.some(item => item.dish._id === dishId);\n  };\n\n  // Calculate totals whenever items change\n  useEffect(() => {\n    dispatch({\n      type: 'CALCULATE_TOTALS'\n    });\n  }, [state.items]);\n\n  // Save cart to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('cartItems', JSON.stringify(state.items));\n  }, [state.items]);\n  return /*#__PURE__*/_jsxDEV(CartContext.Provider, {\n    value: {\n      ...state,\n      addToCart,\n      removeFromCart,\n      updateQuantity,\n      clearCart,\n      getItemQuantity,\n      isInCart\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(CartProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = CartProvider;\nexport const useCart = () => {\n  _s2();\n  const context = useContext(CartContext);\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n_s2(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "toast", "jsxDEV", "_jsxDEV", "CartContext", "initialState", "items", "JSON", "parse", "localStorage", "getItem", "totalAmount", "totalItems", "cartReducer", "state", "action", "type", "existingItem", "find", "item", "dish", "_id", "payload", "updatedItems", "map", "quantity", "filter", "dishId", "reduce", "total", "price", "parseFloat", "toFixed", "CartProvider", "children", "_s", "dispatch", "addToCart", "specialInstructions", "success", "name", "removeFromCart", "info", "updateQuantity", "clearCart", "getItemQuantity", "isInCart", "some", "setItem", "stringify", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useCart", "_s2", "context", "undefined", "Error", "$RefreshReg$"], "sources": ["D:/D Drive/Projects/Restaurant App/frontend/src/context/CartContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { toast } from 'react-toastify';\n\nconst CartContext = createContext();\n\nconst initialState = {\n  items: JSON.parse(localStorage.getItem('cartItems')) || [],\n  totalAmount: 0,\n  totalItems: 0\n};\n\nconst cartReducer = (state, action) => {\n  switch (action.type) {\n    case 'ADD_TO_CART': {\n      const existingItem = state.items.find(item => item.dish._id === action.payload.dish._id);\n      \n      if (existingItem) {\n        const updatedItems = state.items.map(item =>\n          item.dish._id === action.payload.dish._id\n            ? { ...item, quantity: item.quantity + action.payload.quantity }\n            : item\n        );\n        return {\n          ...state,\n          items: updatedItems\n        };\n      } else {\n        return {\n          ...state,\n          items: [...state.items, action.payload]\n        };\n      }\n    }\n    \n    case 'REMOVE_FROM_CART': {\n      const updatedItems = state.items.filter(item => item.dish._id !== action.payload);\n      return {\n        ...state,\n        items: updatedItems\n      };\n    }\n    \n    case 'UPDATE_QUANTITY': {\n      const updatedItems = state.items.map(item =>\n        item.dish._id === action.payload.dishId\n          ? { ...item, quantity: action.payload.quantity }\n          : item\n      ).filter(item => item.quantity > 0);\n      \n      return {\n        ...state,\n        items: updatedItems\n      };\n    }\n    \n    case 'CLEAR_CART':\n      return {\n        ...state,\n        items: []\n      };\n    \n    case 'CALCULATE_TOTALS': {\n      const totalAmount = state.items.reduce(\n        (total, item) => total + (item.dish.price * item.quantity), \n        0\n      );\n      const totalItems = state.items.reduce(\n        (total, item) => total + item.quantity, \n        0\n      );\n      \n      return {\n        ...state,\n        totalAmount: parseFloat(totalAmount.toFixed(2)),\n        totalItems\n      };\n    }\n    \n    default:\n      return state;\n  }\n};\n\nexport const CartProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(cartReducer, initialState);\n\n  // Add item to cart\n  const addToCart = (dish, quantity = 1, specialInstructions = '') => {\n    dispatch({\n      type: 'ADD_TO_CART',\n      payload: {\n        dish,\n        quantity,\n        specialInstructions\n      }\n    });\n    toast.success(`${dish.name} added to cart!`);\n  };\n\n  // Remove item from cart\n  const removeFromCart = (dishId) => {\n    dispatch({\n      type: 'REMOVE_FROM_CART',\n      payload: dishId\n    });\n    toast.info('Item removed from cart');\n  };\n\n  // Update item quantity\n  const updateQuantity = (dishId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(dishId);\n      return;\n    }\n    \n    dispatch({\n      type: 'UPDATE_QUANTITY',\n      payload: { dishId, quantity }\n    });\n  };\n\n  // Clear entire cart\n  const clearCart = () => {\n    dispatch({ type: 'CLEAR_CART' });\n    toast.info('Cart cleared');\n  };\n\n  // Get item quantity\n  const getItemQuantity = (dishId) => {\n    const item = state.items.find(item => item.dish._id === dishId);\n    return item ? item.quantity : 0;\n  };\n\n  // Check if item is in cart\n  const isInCart = (dishId) => {\n    return state.items.some(item => item.dish._id === dishId);\n  };\n\n  // Calculate totals whenever items change\n  useEffect(() => {\n    dispatch({ type: 'CALCULATE_TOTALS' });\n  }, [state.items]);\n\n  // Save cart to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('cartItems', JSON.stringify(state.items));\n  }, [state.items]);\n\n  return (\n    <CartContext.Provider\n      value={{\n        ...state,\n        addToCart,\n        removeFromCart,\n        updateQuantity,\n        clearCart,\n        getItemQuantity,\n        isInCart\n      }}\n    >\n      {children}\n    </CartContext.Provider>\n  );\n};\n\nexport const useCart = () => {\n  const context = useContext(CartContext);\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,MAAMQ,YAAY,GAAG;EACnBC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE;EAC1DC,WAAW,EAAE,CAAC;EACdC,UAAU,EAAE;AACd,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAAE;QAClB,MAAMC,YAAY,GAAGH,KAAK,CAACR,KAAK,CAACY,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAACC,GAAG,KAAKN,MAAM,CAACO,OAAO,CAACF,IAAI,CAACC,GAAG,CAAC;QAExF,IAAIJ,YAAY,EAAE;UAChB,MAAMM,YAAY,GAAGT,KAAK,CAACR,KAAK,CAACkB,GAAG,CAACL,IAAI,IACvCA,IAAI,CAACC,IAAI,CAACC,GAAG,KAAKN,MAAM,CAACO,OAAO,CAACF,IAAI,CAACC,GAAG,GACrC;YAAE,GAAGF,IAAI;YAAEM,QAAQ,EAAEN,IAAI,CAACM,QAAQ,GAAGV,MAAM,CAACO,OAAO,CAACG;UAAS,CAAC,GAC9DN,IACN,CAAC;UACD,OAAO;YACL,GAAGL,KAAK;YACRR,KAAK,EAAEiB;UACT,CAAC;QACH,CAAC,MAAM;UACL,OAAO;YACL,GAAGT,KAAK;YACRR,KAAK,EAAE,CAAC,GAAGQ,KAAK,CAACR,KAAK,EAAES,MAAM,CAACO,OAAO;UACxC,CAAC;QACH;MACF;IAEA,KAAK,kBAAkB;MAAE;QACvB,MAAMC,YAAY,GAAGT,KAAK,CAACR,KAAK,CAACoB,MAAM,CAACP,IAAI,IAAIA,IAAI,CAACC,IAAI,CAACC,GAAG,KAAKN,MAAM,CAACO,OAAO,CAAC;QACjF,OAAO;UACL,GAAGR,KAAK;UACRR,KAAK,EAAEiB;QACT,CAAC;MACH;IAEA,KAAK,iBAAiB;MAAE;QACtB,MAAMA,YAAY,GAAGT,KAAK,CAACR,KAAK,CAACkB,GAAG,CAACL,IAAI,IACvCA,IAAI,CAACC,IAAI,CAACC,GAAG,KAAKN,MAAM,CAACO,OAAO,CAACK,MAAM,GACnC;UAAE,GAAGR,IAAI;UAAEM,QAAQ,EAAEV,MAAM,CAACO,OAAO,CAACG;QAAS,CAAC,GAC9CN,IACN,CAAC,CAACO,MAAM,CAACP,IAAI,IAAIA,IAAI,CAACM,QAAQ,GAAG,CAAC,CAAC;QAEnC,OAAO;UACL,GAAGX,KAAK;UACRR,KAAK,EAAEiB;QACT,CAAC;MACH;IAEA,KAAK,YAAY;MACf,OAAO;QACL,GAAGT,KAAK;QACRR,KAAK,EAAE;MACT,CAAC;IAEH,KAAK,kBAAkB;MAAE;QACvB,MAAMK,WAAW,GAAGG,KAAK,CAACR,KAAK,CAACsB,MAAM,CACpC,CAACC,KAAK,EAAEV,IAAI,KAAKU,KAAK,GAAIV,IAAI,CAACC,IAAI,CAACU,KAAK,GAAGX,IAAI,CAACM,QAAS,EAC1D,CACF,CAAC;QACD,MAAMb,UAAU,GAAGE,KAAK,CAACR,KAAK,CAACsB,MAAM,CACnC,CAACC,KAAK,EAAEV,IAAI,KAAKU,KAAK,GAAGV,IAAI,CAACM,QAAQ,EACtC,CACF,CAAC;QAED,OAAO;UACL,GAAGX,KAAK;UACRH,WAAW,EAAEoB,UAAU,CAACpB,WAAW,CAACqB,OAAO,CAAC,CAAC,CAAC,CAAC;UAC/CpB;QACF,CAAC;MACH;IAEA;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMmB,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACrB,KAAK,EAAEsB,QAAQ,CAAC,GAAGrC,UAAU,CAACc,WAAW,EAAER,YAAY,CAAC;;EAE/D;EACA,MAAMgC,SAAS,GAAGA,CAACjB,IAAI,EAAEK,QAAQ,GAAG,CAAC,EAAEa,mBAAmB,GAAG,EAAE,KAAK;IAClEF,QAAQ,CAAC;MACPpB,IAAI,EAAE,aAAa;MACnBM,OAAO,EAAE;QACPF,IAAI;QACJK,QAAQ;QACRa;MACF;IACF,CAAC,CAAC;IACFrC,KAAK,CAACsC,OAAO,CAAC,GAAGnB,IAAI,CAACoB,IAAI,iBAAiB,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMC,cAAc,GAAId,MAAM,IAAK;IACjCS,QAAQ,CAAC;MACPpB,IAAI,EAAE,kBAAkB;MACxBM,OAAO,EAAEK;IACX,CAAC,CAAC;IACF1B,KAAK,CAACyC,IAAI,CAAC,wBAAwB,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAChB,MAAM,EAAEF,QAAQ,KAAK;IAC3C,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACjBgB,cAAc,CAACd,MAAM,CAAC;MACtB;IACF;IAEAS,QAAQ,CAAC;MACPpB,IAAI,EAAE,iBAAiB;MACvBM,OAAO,EAAE;QAAEK,MAAM;QAAEF;MAAS;IAC9B,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMmB,SAAS,GAAGA,CAAA,KAAM;IACtBR,QAAQ,CAAC;MAAEpB,IAAI,EAAE;IAAa,CAAC,CAAC;IAChCf,KAAK,CAACyC,IAAI,CAAC,cAAc,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,eAAe,GAAIlB,MAAM,IAAK;IAClC,MAAMR,IAAI,GAAGL,KAAK,CAACR,KAAK,CAACY,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAACC,GAAG,KAAKM,MAAM,CAAC;IAC/D,OAAOR,IAAI,GAAGA,IAAI,CAACM,QAAQ,GAAG,CAAC;EACjC,CAAC;;EAED;EACA,MAAMqB,QAAQ,GAAInB,MAAM,IAAK;IAC3B,OAAOb,KAAK,CAACR,KAAK,CAACyC,IAAI,CAAC5B,IAAI,IAAIA,IAAI,CAACC,IAAI,CAACC,GAAG,KAAKM,MAAM,CAAC;EAC3D,CAAC;;EAED;EACA3B,SAAS,CAAC,MAAM;IACdoC,QAAQ,CAAC;MAAEpB,IAAI,EAAE;IAAmB,CAAC,CAAC;EACxC,CAAC,EAAE,CAACF,KAAK,CAACR,KAAK,CAAC,CAAC;;EAEjB;EACAN,SAAS,CAAC,MAAM;IACdS,YAAY,CAACuC,OAAO,CAAC,WAAW,EAAEzC,IAAI,CAAC0C,SAAS,CAACnC,KAAK,CAACR,KAAK,CAAC,CAAC;EAChE,CAAC,EAAE,CAACQ,KAAK,CAACR,KAAK,CAAC,CAAC;EAEjB,oBACEH,OAAA,CAACC,WAAW,CAAC8C,QAAQ;IACnBC,KAAK,EAAE;MACL,GAAGrC,KAAK;MACRuB,SAAS;MACTI,cAAc;MACdE,cAAc;MACdC,SAAS;MACTC,eAAe;MACfC;IACF,CAAE;IAAAZ,QAAA,EAEDA;EAAQ;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACpB,EAAA,CAhFWF,YAAY;AAAAuB,EAAA,GAAZvB,YAAY;AAkFzB,OAAO,MAAMwB,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG7D,UAAU,CAACM,WAAW,CAAC;EACvC,IAAIuD,OAAO,KAAKC,SAAS,EAAE;IACzB,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOF,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAM,YAAA,CAAAN,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}