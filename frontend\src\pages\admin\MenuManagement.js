import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

const MenuManagement = () => {
  const [dishes, setDishes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingDish, setEditingDish] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: 'appetizers',
    ingredients: '',
    allergens: '',
    dietaryInfo: '',
    preparationTime: '',
    spiceLevel: 'mild',
    isAvailable: true,
    isPopular: false
  });
  const [imageFile, setImageFile] = useState(null);

  useEffect(() => {
    fetchDishes();
  }, []);

  const fetchDishes = async () => {
    try {
      const response = await axios.get('/api/menu?limit=100');
      setDishes(response.data.dishes);
    } catch (error) {
      toast.error('Failed to load dishes');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleImageChange = (e) => {
    setImageFile(e.target.files[0]);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      category: 'appetizers',
      ingredients: '',
      allergens: '',
      dietaryInfo: '',
      preparationTime: '',
      spiceLevel: 'mild',
      isAvailable: true,
      isPopular: false
    });
    setImageFile(null);
    setEditingDish(null);
    setShowForm(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const submitData = new FormData();
    Object.keys(formData).forEach(key => {
      submitData.append(key, formData[key]);
    });
    
    if (imageFile) {
      submitData.append('image', imageFile);
    }

    try {
      if (editingDish) {
        await axios.put(`/api/menu/${editingDish._id}`, submitData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        toast.success('Dish updated successfully!');
      } else {
        await axios.post('/api/menu', submitData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        toast.success('Dish created successfully!');
      }
      
      fetchDishes();
      resetForm();
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to save dish');
    }
  };

  const handleEdit = (dish) => {
    setFormData({
      name: dish.name,
      description: dish.description,
      price: dish.price,
      category: dish.category,
      ingredients: dish.ingredients?.join(', ') || '',
      allergens: dish.allergens?.join(', ') || '',
      dietaryInfo: dish.dietaryInfo?.join(', ') || '',
      preparationTime: dish.preparationTime,
      spiceLevel: dish.spiceLevel,
      isAvailable: dish.isAvailable,
      isPopular: dish.isPopular
    });
    setEditingDish(dish);
    setShowForm(true);
  };

  const handleDelete = async (dishId) => {
    if (window.confirm('Are you sure you want to delete this dish?')) {
      try {
        await axios.delete(`/api/menu/${dishId}`);
        toast.success('Dish deleted successfully!');
        fetchDishes();
      } catch (error) {
        toast.error('Failed to delete dish');
      }
    }
  };

  if (loading) {
    return <div className="loading">Loading menu items...</div>;
  }

  return (
    <div className="menu-management">
      <div className="container">
        <div className="page-header">
          <h1>Menu Management</h1>
          <button 
            className="btn btn-primary"
            onClick={() => setShowForm(true)}
          >
            Add New Dish
          </button>
        </div>

        {showForm && (
          <div className="modal-overlay" onClick={() => setShowForm(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h2>{editingDish ? 'Edit Dish' : 'Add New Dish'}</h2>
                <button className="close-btn" onClick={resetForm}>×</button>
              </div>

              <form onSubmit={handleSubmit} className="dish-form">
                <div className="form-row">
                  <div className="form-group">
                    <label>Name</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>Price</label>
                    <input
                      type="number"
                      name="price"
                      value={formData.price}
                      onChange={handleInputChange}
                      step="0.01"
                      required
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label>Description</label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows="3"
                    required
                  />
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Category</label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                    >
                      <option value="appetizers">Appetizers</option>
                      <option value="main-courses">Main Courses</option>
                      <option value="desserts">Desserts</option>
                      <option value="beverages">Beverages</option>
                      <option value="specials">Specials</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Preparation Time (minutes)</label>
                    <input
                      type="number"
                      name="preparationTime"
                      value={formData.preparationTime}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Spice Level</label>
                    <select
                      name="spiceLevel"
                      value={formData.spiceLevel}
                      onChange={handleInputChange}
                    >
                      <option value="mild">Mild</option>
                      <option value="medium">Medium</option>
                      <option value="hot">Hot</option>
                      <option value="very-hot">Very Hot</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Image</label>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label>Ingredients (comma separated)</label>
                  <input
                    type="text"
                    name="ingredients"
                    value={formData.ingredients}
                    onChange={handleInputChange}
                    placeholder="tomato, cheese, basil"
                  />
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Allergens (comma separated)</label>
                    <input
                      type="text"
                      name="allergens"
                      value={formData.allergens}
                      onChange={handleInputChange}
                      placeholder="gluten, dairy, nuts"
                    />
                  </div>
                  <div className="form-group">
                    <label>Dietary Info (comma separated)</label>
                    <input
                      type="text"
                      name="dietaryInfo"
                      value={formData.dietaryInfo}
                      onChange={handleInputChange}
                      placeholder="vegetarian, gluten-free"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>
                      <input
                        type="checkbox"
                        name="isAvailable"
                        checked={formData.isAvailable}
                        onChange={handleInputChange}
                      />
                      Available
                    </label>
                  </div>
                  <div className="form-group">
                    <label>
                      <input
                        type="checkbox"
                        name="isPopular"
                        checked={formData.isPopular}
                        onChange={handleInputChange}
                      />
                      Popular Item
                    </label>
                  </div>
                </div>

                <div className="form-actions">
                  <button type="button" className="btn btn-secondary" onClick={resetForm}>
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-primary">
                    {editingDish ? 'Update Dish' : 'Create Dish'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        <div className="dishes-grid">
          {dishes.map((dish) => (
            <div key={dish._id} className="dish-card admin">
              <img 
                src={`http://localhost:5000/uploads/${dish.image}`}
                alt={dish.name}
                onError={(e) => {
                  e.target.src = '/api/placeholder/300/200';
                }}
              />
              <div className="dish-info">
                <h3>{dish.name}</h3>
                <p className="price">${dish.price}</p>
                <p className="category">{dish.category}</p>
                <div className="dish-status">
                  {dish.isAvailable ? (
                    <span className="status available">Available</span>
                  ) : (
                    <span className="status unavailable">Unavailable</span>
                  )}
                  {dish.isPopular && (
                    <span className="status popular">Popular</span>
                  )}
                </div>
                <div className="dish-actions">
                  <button 
                    className="btn btn-secondary"
                    onClick={() => handleEdit(dish)}
                  >
                    Edit
                  </button>
                  <button 
                    className="btn btn-danger"
                    onClick={() => handleDelete(dish._id)}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MenuManagement;
