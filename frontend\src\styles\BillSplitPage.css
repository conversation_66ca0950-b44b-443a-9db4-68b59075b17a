.bill-split-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px 0;
}

.bill-split-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.restaurant-info h1 {
  color: #e74c3c;
  margin: 0 0 10px 0;
  font-size: 2rem;
}

.restaurant-info p {
  color: #7f8c8d;
  margin: 5px 0;
}

.order-info h2 {
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.payment-progress {
  text-align: right;
}

.progress-bar {
  width: 200px;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #e74c3c, #27ae60);
  transition: width 0.3s ease;
}

.payment-progress p {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

.bill-summary {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.order-items,
.split-info {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.order-items h3,
.split-info h3 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 1.3rem;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.item-row:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.item-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.item-details h4 {
  color: #2c3e50;
  margin: 0 0 5px 0;
  font-size: 1rem;
}

.item-details p {
  color: #7f8c8d;
  margin: 2px 0;
  font-size: 14px;
}

.item-total {
  font-weight: bold;
  color: #e74c3c;
  font-size: 1.1rem;
}

.bill-totals {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 2px solid #eee;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #7f8c8d;
}

.total-row.grand-total {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
  padding-top: 10px;
  border-top: 1px solid #ddd;
}

.split-summary-info p {
  margin: 8px 0;
  color: #7f8c8d;
}

.split-summary-info strong {
  color: #2c3e50;
}

.participants-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.participants-section h3 {
  color: #2c3e50;
  margin: 0 0 25px 0;
  font-size: 1.3rem;
}

.all-paid-banner {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 25px;
}

.all-paid-banner h2 {
  margin: 0 0 10px 0;
}

.all-paid-banner p {
  margin: 0;
  opacity: 0.9;
}

.participants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.participant-card {
  border: 2px solid #eee;
  border-radius: 12px;
  padding: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.participant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.participant-card.paid {
  border-color: #27ae60;
  background: #f8fff8;
}

.participant-card.pending {
  border-color: #f39c12;
  background: #fffbf0;
}

.participant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.participant-header h4 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.1rem;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
}

.participant-amount {
  text-align: center;
  margin-bottom: 15px;
}

.amount {
  font-size: 1.5rem;
  font-weight: bold;
  color: #e74c3c;
}

.participant-items {
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.participant-items h5 {
  color: #2c3e50;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.participant-item {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  color: #7f8c8d;
  margin-bottom: 4px;
}

.payment-section {
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.payment-methods {
  margin-bottom: 15px;
}

.payment-methods label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.payment-methods select {
  width: 100%;
  padding: 8px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.pay-btn {
  width: 100%;
  padding: 12px;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.pay-btn:hover {
  background: #229954;
}

.paid-info {
  text-align: center;
  color: #27ae60;
}

.paid-time {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 5px;
}

.contact-info {
  font-size: 13px;
  color: #7f8c8d;
  margin-top: 8px;
}

.bill-split-footer {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.share-section h4,
.help-section h4 {
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.share-link {
  display: flex;
  gap: 10px;
}

.share-input {
  flex: 1;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: #f8f9fa;
}

.copy-btn {
  padding: 10px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.copy-btn:hover {
  background: #2980b9;
}

.help-section p {
  color: #7f8c8d;
  margin: 0;
}

.error-message {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.error-message h2 {
  color: #e74c3c;
  margin: 0 0 15px 0;
}

.error-message p {
  color: #7f8c8d;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .bill-split-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .payment-progress {
    text-align: center;
  }

  .progress-bar {
    width: 100%;
    max-width: 300px;
  }

  .bill-summary {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .participants-grid {
    grid-template-columns: 1fr;
  }

  .bill-split-footer {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .share-link {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .bill-split-page {
    padding: 10px 0;
  }

  .bill-split-header,
  .order-items,
  .split-info,
  .participants-section,
  .bill-split-footer {
    padding: 20px 15px;
  }

  .restaurant-info h1 {
    font-size: 1.5rem;
  }

  .item-info {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .item-row {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}
